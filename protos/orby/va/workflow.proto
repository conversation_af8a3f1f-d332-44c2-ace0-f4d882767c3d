syntax = "proto3";

package orby.va;

import "common/user_profile.proto";
import "google/protobuf/timestamp.proto";

// Workflow message for alpha implementation
message Workflow {
  // Workflow status enum
  enum Status {
    STATUS_UNSPECIFIED = 0;
    STATUS_DRAFT = 1;
    STATUS_COMPLETED = 2;
  }
  
  // Unique identifier for the workflow
  string id = 1;
  
  // Display name of the workflow
  string display_name = 2;
  
  // Organization ID
  string org_id = 3;
  
  // Creator information
  common.UserProfileInfo creator = 4;
  
  // Timestamps
  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp updated_at = 6;
  
  // Git repository and generated files
  GitRepository git_repository = 7;
  WorkflowGeneratedFiles generated_files = 8;
  
  // Status of the workflow
  Status status = 9;
}

// Git repository information
message GitRepository {
  string repo_url = 1;
  string commit_hash = 2;
  optional string branch = 3;
}

// Generated files referenced by user file IDs
message WorkflowGeneratedFiles {
  // User file ID for generated SOP markdown
  string sop_file_id = 1;
  // User file ID for manifest.json
  string manifest_file_id = 2;
}

message CreateWorkflowRequest {
  // All fields handled by middleware - minimal request
  // Could add display_name here if needed at creation time
}

// Update workflow request - single update when workflow is complete and ready
message UpdateWorkflowRequest {
  string id = 1;
  
  // All final data when workflow is complete
  string display_name = 2;
  GitRepository git_repository = 3;
  WorkflowGeneratedFiles generated_files = 4;
}

// Filter message for workflow listing
message ListWorkflowsFilter {
  // Filter by creator ID
  repeated string creator_ids = 1;
  
  // Filter by display name prefix (case-insensitive)
  string display_name_prefix = 2;
}

message ListWorkflowsRequest {
  // Default is 10 (when page_size is missing or set to 0). Max value is 20
  int32 page_size = 1;
  
  // Page number - indexed from 1
  int32 page_number = 2;
  
  // structured filter for workflows
  ListWorkflowsFilter filter = 3;
}

message ListWorkflowsResponse {
  repeated Workflow workflows = 1;
  
  // Total available workflows size
  int32 total_size = 2;
}

message DeleteWorkflowRequest {
  string id = 1;
}

message GetWorkflowRequest {
  string id = 1;
}

message GetWorkflowCreatorsResponse {
  repeated common.UserProfileInfo creators = 1;
}
