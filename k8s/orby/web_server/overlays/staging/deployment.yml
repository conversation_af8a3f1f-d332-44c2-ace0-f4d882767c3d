apiVersion: apps/v1
kind: Deployment
metadata:
  name: va-web-server-deployment
spec:
  template:
    spec:
      containers:
        - name: http-server
          env:
            - name: MODE
              value: staging
            - name: CHAINLIT_DEFAULT_BUCKET
              value: 'va_chainlit_data_staging'
            - name: GOO<PERSON>LE_CLOUD_PROJECT_ID
              value: orby-ai-backend-dev
            - name: DEFAULT_SERVICE_ACCOUNT
              value: <EMAIL>
            - name: LANGSMITH_PROJECT
              value: 'vibe-automation-server-staging'
            - name: LANGSMITH_TRACING
              value: 'true'
        - name: grpc-server
          env:
            - name: MODE
              value: staging
            - name: GOOGLE_CLOUD_PROJECT_ID
              value: orby-ai-backend-dev
            - name: DEFAULT_SERVICE_ACCOUNT
              value: <EMAIL>
