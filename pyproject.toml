[project]
name = "vibe-automation-server"
version = "0.1.0"
description = "Monorepo for the vibe automation server"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "grpcio>=1.73.1",
    "mypy-protobuf>=3.6.0",
    "python-dotenv>=1.1.0",
    "grpcio-tools>=1.71.0",
    "execution",
    "web_server",
    "agent",
    "protos_gen",
    "grpcio-reflection>=1.71.0",
    "chainlit>=2.5.5",
    "langgraph>=0.2.0",
    "langgraph-supervisor>=0.0.27",
    "langchain>=0.3.0",
    "langchain-anthropic>=0.3.0",
    "langchain-openai>=0.2.0",
    "beautifulsoup4>=4.12.0",
    "lxml>=4.9.0",
    "langchain-core>=0.3.0",
    "langchain-community>=0.3.0",
    "langchain-text-splitters>=0.3.0",
    "openai>=1.12.0",
    "langchain-tavily>=0.1.0",
    "claude-code-sdk>=0.0.10",
    "boto3>=1.38.36",
    "google-auth>=2.40.3",
    "bs4>=0.0.2",
    "langsmith>=0.2.0",
    "e2b>=1.5.2",
    "opencv-python>=4.8.0",
    "ffmpeg",
    "langchain-google-vertexai>=2.0.25",
    "asyncpg>=0.30.0",
    "google-cloud-storage>=2.19.0",
    "orbot",
    "redis>=6.2.0",
    "pydantic>=2.11.7",
]

[tool.uv]
required-version = ">=0.7.13"
override-dependencies = [
    "protobuf==6.31.0",
]


[tool.uv.workspace]
members = [
    "packages/execution",
    "packages/common",
    "packages/web_server",
    "packages/protos_gen",
    "packages/agent",
    "packages/chainlit_chat",    
    "packages/orbot",
]

[tool.uv.sources]
protos_gen = { workspace = true }
agent = { workspace = true }
execution = { workspace = true }
web_server = { workspace = true }
orbot = { workspace = true }

[tool.ruff]
line-length = 80
target-version = "py313"
# Force consistent behavior across operating systems
respect-gitignore = true
force-exclude = true

extend-exclude = [
    "__pycache__",
    ".eggs",
    ".git",
    ".venv",
    "build",
    "dist",
    "notebooks",
    ".cache",
    "packages/protos_gen",
]

[tool.ruff.lint]
select = ["E", "F", "I", "UP", "B", "W292"]
# E501: Line too long, since we use ruff format to fix this
ignore = ["E501"]

# Configure import sorting - defines our workspace packages as first-party imports
[tool.ruff.lint.isort]
known-first-party = ["execution", "web_server", "common", "protos_gen", "agent", "orby"]
# Force consistent import sorting across OS
force-single-line = false
force-sort-within-sections = true
case-sensitive = false

[tool.ruff.format]
# Force LF line endings for consistency across all operating systems
line-ending = "lf"
indent-style = "space"
quote-style = "double"
# Ensure consistent trailing newlines
skip-magic-trailing-comma = false

[dependency-groups]
dev = [
    "coverage>=7.8.2",
    "mypy>=1.16.0",
    "ruff==0.12.3",
    "pytest>=8.4.0",
    "pytest-asyncio>=1.0.0",
    "pytest-mongo>=3.2.0",
]

[tool.pytest.ini_options]
addopts = ["-ra", "-q"]
pythonpath = [
    ".", "packages/execution", "packages/common", "packages/web_server"
]
