"""Bash tool for executing shell commands using the sandbox runtime."""

import asyncio
import re

from langchain_core.tools import ToolException
from langchain_core.tools.base import ArgsSchema
from pydantic import BaseModel, Field

from .base_tool import RuntimeBaseTool


class BashToolInput(BaseModel):
    """Input schema for the bash tool."""
    command: str = Field(
        description="The bash command to execute. Commands maintain state - running code, installing packages, or managing files, and files persist between calls. This tool operates in a workspace. Use relative paths when possible."
    )


class BashTool(RuntimeBaseTool):
    """Tool for executing bash commands using the sandbox runtime."""
    
    name: str = "bash"
    description: str = "Execute bash commands in a secure sandbox environment. Commands maintain state across calls - Use for running code, installing packages, or managing files.."
    args_schema: ArgsSchema | None = BashToolInput
    return_direct: bool = False
    
    # Allow extra fields for tool-specific configuration
    model_config = {"extra": "allow"}
    
    def __init__(self, 
                 timeout: int = 120,
                 max_output_size: int = 50000,
                 **kwargs):
        super().__init__(**kwargs)
        
        # Tool-specific configuration
        self.timeout = timeout
        self.max_output_size = max_output_size
        self.command_count = 0
        self.handle_tool_error = True
        
        # Security: dangerous command patterns to block
        self._dangerous_patterns = [
            r'rm\s+-rf\s+/',
            r'format\s+',
            r'mkfs\s+', 
            r'dd\s+if=.*of=/dev/',
            r':(){ :|:& };:',  # Fork bomb
            r'sudo\s+rm\s+-rf',
            r'chmod\s+-R\s+777\s+/',
            r'chown\s+-R\s+.*\s+/',
            r'find\s+.*-exec\s+rm',
            r'shutdown\s+',
            r'reboot\s+',
            r'halt\s+',
            r'poweroff\s+',
            r'init\s+[06]',
            r'kill\s+-9\s+-1',
            r'killall\s+-9',
            r'pkill\s+-9',
        ]
        
        # Compile patterns for performance
        self._compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self._dangerous_patterns]
    
    def _is_command_dangerous(self, command: str) -> bool:
        """Check if a command matches dangerous patterns."""
        for pattern in self._compiled_patterns:
            if pattern.search(command):
                return True
        return False
    
    def _sanitize_output(self, output: str) -> str:
        """Sanitize command output."""
        # Remove ANSI escape codes
        ansi_escape = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
        cleaned = ansi_escape.sub('', output)
        
        # Truncate if too long
        if len(cleaned) > self.max_output_size:
            cleaned = cleaned[:self.max_output_size] + "\n... (output truncated)"
        
        return cleaned
    
    
    def _run(
        self,
        command: str,
    ) -> str:
        """Synchronous execution - delegates to async implementation."""
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If we're in an async context, we need to handle this differently
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, self._arun(command))
                    return future.result()
            else:
                return loop.run_until_complete(self._arun(command))
        except RuntimeError as e:
            if "no current event loop" in str(e):
                return asyncio.run(self._arun(command,))
            raise ToolException(f"Failed to execute bash command: {e}") from e
    
    async def _arun(
        self,
        command: str
    ) -> str:
        """Execute the bash command asynchronously."""
        command = command.strip()
        
        # Validate command
        if not command:
            raise ToolException("Empty command provided")
        
        # Check for dangerous patterns
        if self._is_command_dangerous(command):
            raise ToolException("Command blocked for security reasons")
        
        # Ensure runtime is available
        if not await self._ensure_runtime() or not self.runtime:
            raise ToolException("Failed to ensure runtime")
        
        # Execute command using persistent bash session
        result = await self.runtime.run_bash_command(command=command)
        
        self.command_count += 1
        self.logger.info(f"Executed command #{self.command_count}: {command[:50]}...")
        
        # The runtime already handles stderr merging for bash sessions
        # Just sanitize the stdout output
        clean_output = self._sanitize_output(result.stdout)
        
        # If command failed, include error information in output
        if result.returncode != 0:
            clean_output += f"\n[Command failed with exit code {result.returncode}. Result: {result}]"
        
        return clean_output
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    

    def __del__(self):
        """Cleanup on deletion."""
        # Note: Can't call async method in __del__, 
        # so we rely on proper cleanup in context manager or explicit calls
        pass
