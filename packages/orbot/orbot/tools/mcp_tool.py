"""
MCP Tool adapter for integrating Model Control Protocol tools with Orbot.
This adapter wraps MCP tools as LangChain tools for use in the agent.
"""

import json
import typing
from typing import Any

import chainlit as cl
from chainlit.session import WebsocketSession
from langchain_core.tools import BaseTool
from pydantic import Field

from common.log import debug, error, info


class MCPTool(BaseTool):
    """
    Adapter that wraps an MCP tool as a LangChain tool.
    This allows MCP tools to be used seamlessly within the Orbot agent.
    """

    mcp_server: str
    tool_schema: dict[str, Any] = Field(default_factory=dict)

    
    def __init__(self, mcp_server: str, tool_name: str, tool_description: str, 
                 tool_schema: dict[str, Any] | None = None, **kwargs):
        """
        Initialize an MCP tool adapter.
        
        Args:
            mcp_server: Name of the MCP server providing this tool
            tool_name: Name of the tool in the MCP server
            tool_description: Description of what the tool does
            tool_schema: JSON schema for the tool's input parameters
        """
        super().__init__(
            name=f"mcp_{mcp_server}_{tool_name}",
            description=tool_description,
            mcp_server=mcp_server,
            tool_schema=tool_schema or {},
            **kwargs
        )
        self._original_tool_name = tool_name
    
    def _run(self, tool_input: dict[str, Any]) -> str:
        """Synchronous execution not supported for MCP tools."""
        raise NotImplementedError("MCP tools only support async execution")
    
    async def _arun(self, tool_input: dict[str, Any]) -> str:
        """
        Execute the MCP tool asynchronously.
        
        Args:
            tool_input: Input parameters for the tool
            
        Returns:
            Tool execution result as a string
        """
        try:
            # Get the MCP sessions from state manager
            session = typing.cast(WebsocketSession, cl.context.session)
            
            if not session.mcp_sessions:
                return "Error: No MCP sessions available"
            
            if self.mcp_server not in session.mcp_sessions:
                return f"Error: MCP server '{self.mcp_server}' is not connected"
            
            mcp_session, _ = session.mcp_sessions[self.mcp_server]
            
            # Log the tool execution
            info(f"Executing MCP tool '{self._original_tool_name}' on server '{self.mcp_server}'")
            debug(f"Tool input: {json.dumps(tool_input, indent=2)}")
            
            # Call the MCP tool
            result = await mcp_session.call_tool(self._original_tool_name, tool_input)
            
            # Handle the result
            if isinstance(result, dict):
                # Check for error in result
                if "error" in result:
                    error_msg = result.get("error", "Unknown error")
                    error(f"MCP tool error: {error_msg}")
                    return f"Error: {error_msg}"
                
                # Extract content from result
                if "content" in result:
                    content = result["content"]
                    if isinstance(content, list) and len(content) > 0:
                        # Handle content array (common in MCP responses)
                        text_parts = []
                        for item in content:
                            if isinstance(item, dict) and "text" in item:
                                text_parts.append(item["text"])
                            elif isinstance(item, str):
                                text_parts.append(item)
                        return "\n".join(text_parts)
                    elif isinstance(content, str):
                        return content
                    else:
                        return json.dumps(content, indent=2)
                
                # Fallback to full result
                return json.dumps(result, indent=2)
            
            # Handle string results
            return str(result)
            
        except Exception as e:
            error(f"Error executing MCP tool '{self._original_tool_name}': {e}")
            return f"Error executing MCP tool: {str(e)}"
