[project]
name = "agent"
version = "0.1.0"
description = "agent package"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "protos_gen",
    "pydantic>=2.5.0",
    "typing-extensions>=4.8.0",
    "python-dotenv>=1.0.0",
    "pathlib>=1.0.0",
    "matplotlib>=3.7.0",
    "structlog>=23.2.0",
    "langgraph>=0.4.8",
    "langchain-core>=0.2.19",
    "langchain-google-genai>=1.0.6",
    "vibe-automation>=0.4.1",
    "beautifulsoup4>=4.13.4",
    "claude-code-sdk>=0.0.10",
    "web_server",
    "langsmith>=0.2.0",
]

[tool.uv.sources]
web_server = { workspace = true }


[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["agent"]
