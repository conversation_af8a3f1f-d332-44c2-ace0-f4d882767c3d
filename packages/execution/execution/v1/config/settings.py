"""Configuration settings for the execution server v1."""

from dataclasses import dataclass
import os

from dotenv import load_dotenv

load_dotenv()


@dataclass
class ServerConfig:
    """Server configuration settings."""

    port: int = int(os.getenv("GRPC_PORT", "9090"))
    # This defines the number of threads that can be used to process requests
    max_workers: int = int(os.getenv("MAX_WORKERS", "100"))
    mode: str = os.getenv("MODE", "local")


# Global config instance
config = ServerConfig()
