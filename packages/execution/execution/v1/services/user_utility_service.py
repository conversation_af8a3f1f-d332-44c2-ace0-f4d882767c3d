"""User utility service implementation."""

import grpc

from common.interceptors.grpc.auth import get_and_validate_auth_payload
from common.log import error
from common.models.organization import Organization
from common.repository.org_repo import OrganizationRepository
from common.repository.user_repo import UserRepository


class UserUtilityService:
    """Service class for user utility operations."""

    def __init__(self):
        """Initialize the service."""
        self.user_repo = UserRepository()
        self.org_repo = OrganizationRepository()

    async def get_user_organizations(
        self, context: grpc.aio.ServicerContext
    ) -> list[Organization]:
        """
        Get user organizations.

        Args:
            context: The gRPC context containing metadata

        Returns:
            list[Organization]: List of organizations the user is a member of
        """
        try:
            # Extract auth information from gRPC context
            auth_payload = get_and_validate_auth_payload()

            if not auth_payload.user_id:
                raise grpc.RpcError(
                    grpc.StatusCode.UNAUTHENTICATED, "User not authenticated"
                )

            # Get user by id
            user = await self.user_repo.get_by_id(auth_payload.user_id)
            if not user:
                raise grpc.RpcError(grpc.StatusCode.NOT_FOUND, "User not found")

            organizations = []

            # Get all organizations that the user is a member of
            if user.org_ids:
                for org_id in user.org_ids:
                    org = await self.org_repo.get_by_id(org_id)
                    if org:
                        organizations.append(org)

            return organizations

        except Exception as e:
            error(f"Error getting user organizations: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details("Internal server error")
            raise
