"""Business service for execution operations."""

import uuid

from bson import ObjectId
from orby.va.browser_session_pb2 import CreateSessionRequest
from orby.va.execution_management_pb2 import (
    DeleteExecutionRequest,
    GetExecutionRequest,
    ListExecutionsRequest,
    ListExecutionsResponse,
)
from orby.va.public.execution_messages_pb2 import (
    EnvironmentVariable,
    Execution,
    ExecutionStatus,
    GetReviewStatusRequest,
    GetReviewStatusResponse,
    RequestReviewRequest,
    RequestReviewResponse,
    ReviewCompletedRequest,
    ReviewCompletedResponse,
    StartExecutionRequest,
    StartExecutionResponse,
    UpdateExecutionRequest,
)

from common.cache.redis_client import get_redis_client
from common.log import error
from common.models.execution import Execution as ExecutionModel
from common.models.organization import TenantInfo
from common.models.reviews import ReviewStatus
from common.repository.execution_repo import ExecutionRepository
from common.repository.review_repo import ReviewRepository
from common.sandbox.log_streamer import ExecutionRedisContentType
from common.utils.execution_mapper import execution_model_to_proto
from common.utils.gcs_utils import generate_signed_url
from execution.v1.services.browser_service import BrowserService


class ExecutionService:
    def __init__(self):
        self.browser_service = BrowserService()
        self.review_repo = ReviewRepository()
        self.execution_repo = ExecutionRepository()

    # ------------------------------------------------------------------
    # Internal helpers
    # ------------------------------------------------------------------

    def _create_browser_session(self, workflow_id: str, execution_id: str):
        browser_session_args = CreateSessionRequest(
            workflow_id=workflow_id,
            execution_id=execution_id,
        )
        browser_session_details = self.browser_service.create_session(
            browser_session_args
        )
        session_url, live_view_url = (
            browser_session_details.connection_url,
            browser_session_details.live_view_url,
        )
        return session_url, live_view_url

    async def _create_review(
        self,
        execution_id: str,
        user_message: str = "",
        user_response: str | None = None,
    ):
        try:
            review_id = await self.review_repo.create_review(
                execution_id=execution_id,
                user_message=user_message,
                user_response=user_response,
            )
            return review_id, ReviewStatus.PENDING.value
        except Exception as e:
            error(f"Error creating review: {e}")
            raise

    async def _update_review_status(
        self,
        review_id: str,
        status: str = ReviewStatus.READY.value,
    ):
        try:
            success = await self.review_repo.update_review_status(
                ObjectId(review_id), status
            )
            if not success:
                raise RuntimeError("Failed to update review status")
            return review_id, status
        except Exception as e:
            error(f"Error updating review status: {e}")
            raise

    def start_execution(
        self, request: StartExecutionRequest
    ) -> StartExecutionResponse:
        """Start a new execution.
        Generates a UUID for the execution, caches the request for later use,
        and seeds the event stream with a placeholder *execution_started* event.
        """
        execution_id = str(uuid.uuid4())
        try:
            # Convert environment variables to list of dicts
            env_vars = request.environment

            # Find COMMIT_HASH from the environment variables
            commit_hash = None
            for env_var in env_vars:
                if env_var.name == "COMMIT_HASH":
                    commit_hash = env_var.value
                    break

            if not commit_hash:
                raise ValueError(
                    "Missing required COMMIT_HASH in environment variables for execution"
                )
            session_url, live_view_url = self._create_browser_session(
                request.workflow_id, execution_id
            )

            # Add session_url to environment variables as connection_url
            env_vars.append(
                EnvironmentVariable(name="CONNECTION_URL", value=session_url)
            )
            return StartExecutionResponse(
                execution_id=execution_id, live_view_url=live_view_url
            )
        except Exception as e:
            error(f"Error starting execution: {e}")
            raise e

    async def request_review(
        self, request: RequestReviewRequest
    ) -> RequestReviewResponse:
        """Request a review for the execution"""
        try:
            # Create a review in the database
            review_id, review_status = await self._create_review(
                execution_id=request.execution_id,
                user_message=request.user_message,
            )

            # After we create a review we will also push a message to the redis channel, so that the corresponding log stream subscriber can pick it up
            # and update the review status in the database
            content = f"review_requested {request.execution_id} {review_id} {request.user_message}"
            redis_client = get_redis_client()
            await redis_client.publish_stream(
                f"execution:{request.execution_id}",
                ExecutionRedisContentType(
                    stream_type="stdout",
                    content=content,
                ),
            )

            # Return success status for the review request
            return RequestReviewResponse(
                status=review_status, review_id=str(review_id)
            )
        except Exception as e:
            error(f"Error requesting review: {e}")
            raise e

    async def get_review_status(
        self, request: GetReviewStatusRequest
    ) -> GetReviewStatusResponse:
        """Get the current status of a review"""
        try:
            # Query the review status in the database
            if not request.review_id:
                raise ValueError(
                    "Review ID must be provided to get review status"
                )
            result = await self.review_repo.get_review_status(
                ObjectId(request.review_id)
            )
            if not result:
                # Client may request a review status before the review is created
                # Return empty response in this case
                return GetReviewStatusResponse()
            # Return success status for the review completion
            return GetReviewStatusResponse(
                status=result,
            )
        except Exception as e:
            error(f"Error fetching review status: {e}")
            raise

    async def mark_review_completed(
        self, request: ReviewCompletedRequest
    ) -> ReviewCompletedResponse:
        """Mark a review as completed"""
        try:
            # Update the review status in the database
            review_id, review_status = await self._update_review_status(
                review_id=request.review_id,
                status=ReviewStatus.READY.value,
            )

            # Return success status for the review completion
            return ReviewCompletedResponse(status=review_status)
        except Exception as e:
            error(f"Error marking review as completed: {e}")
            raise e

    async def get_execution(
        self, request: GetExecutionRequest, tenant_info: TenantInfo
    ) -> Execution:
        """Get an execution by its ID"""

        if not ObjectId.is_valid(request.execution_id):
            raise ValueError("Invalid execution ID")
        execution_id = ObjectId(request.execution_id)

        execution = await self.execution_repo.get_execution_by_id(
            execution_id, tenant_info
        )
        if not execution:
            raise ValueError(
                f"Execution with ID {request.execution_id} not found"
            )

        # Convert recording URL to signed URL (if present)
        if execution.rrweb_recording_gcs_uri:
            execution.rrweb_recording_gcs_uri = await generate_signed_url(
                execution.rrweb_recording_gcs_uri
            )

        return execution_model_to_proto(execution)

    # ------------------------------------------------------------------
    # New helper methods for external use
    # ------------------------------------------------------------------

    async def create_execution(
        self,
        execution: ExecutionModel,
        tenant_info: TenantInfo | None = None,
    ) -> ExecutionModel:
        """Create a new execution record.

        This is a thin wrapper around ``ExecutionRepository.create_execution`` so
        that external callers can interact solely with ``ExecutionService``
        without importing the repository layer directly.
        """
        return await self.execution_repo.create_execution(
            execution, tenant_info
        )

    async def update_execution_status(
        self,
        execution_id: str,
        tenant_info: TenantInfo | None,
        status: str,
    ) -> ExecutionModel:
        """Update the status of an execution.

        Args:
            execution_id: The execution's identifier (as a string).
            tenant_info: Tenant information for scoping.
            status: New status value (one of the ``ExecutionStatus`` enum names).
        """
        if not ObjectId.is_valid(execution_id):
            raise ValueError("Invalid execution ID")

        return await self.execution_repo.update_execution(
            ObjectId(execution_id),
            tenant_info=tenant_info,
            status=status,
        )

    async def update_execution_recording_url(
        self,
        execution_id: str,
        tenant_info: TenantInfo | None,
        recording_url: str,
    ) -> ExecutionModel:
        """Update rrweb recording URL for an execution.

        Args:
            execution_id: Execution ID as a hex string.
            tenant_info: Tenant context.
            recording_url: GCS URI of the uploaded recording.
        """

        if not ObjectId.is_valid(execution_id):
            raise ValueError("Invalid execution ID")

        return await self.execution_repo.update_execution(
            ObjectId(execution_id),
            tenant_info=tenant_info,
            rrweb_recording_gcs_uri=recording_url,
        )

    async def list_executions(
        self, request: ListExecutionsRequest, tenant_info: TenantInfo
    ) -> ListExecutionsResponse:
        """List executions by workflow ID with pagination"""

        if not ObjectId.is_valid(request.workflow_id):
            raise ValueError("Invalid workflow ID")
        workflow_id = ObjectId(request.workflow_id)

        if request.page_size == 0:
            request.page_size = 10
        elif request.page_size > 50:
            raise ValueError("Page size must be less than or equal to 50")

        if request.page_number <= 0:
            raise ValueError("Page number must be greater than 0")

        offset = (request.page_number - 1) * request.page_size

        executions = await self.execution_repo.list_executions(
            workflow_id, request.page_size, offset, tenant_info
        )

        total_size = await self.execution_repo.count_executions(
            workflow_id, tenant_info
        )

        proto_executions = [
            execution_model_to_proto(execution_model)
            for execution_model in executions
        ]

        return ListExecutionsResponse(
            executions=proto_executions,
            total_size=total_size,
        )

    async def delete_execution(
        self, request: DeleteExecutionRequest, tenant_info: TenantInfo
    ) -> None:
        """Delete an execution by its ID"""

        if not ObjectId.is_valid(request.execution_id):
            raise ValueError("Invalid execution ID")
        execution_id = ObjectId(request.execution_id)

        success = await self.execution_repo.delete_execution(
            execution_id, tenant_info
        )

        if not success:
            raise ValueError(
                f"Execution with ID {request.execution_id} not found"
            )

        return None

    async def update_execution(
        self,
        request: UpdateExecutionRequest,
        tenant_info: TenantInfo,
    ) -> Execution:
        """Update an execution by its ID, only status is supported for now"""

        if not ObjectId.is_valid(request.execution_id):
            raise ValueError("Invalid execution ID")
        execution_id = ObjectId(request.execution_id)

        request_fields = request.field_mask.paths
        if "status" in request_fields:
            status = ExecutionStatus.Name(request.status)
            execution = await self.execution_repo.update_execution(
                execution_id, tenant_info=tenant_info, status=status
            )
        else:
            raise ValueError("Invalid field mask, available fields: [status]")
        return execution_model_to_proto(execution)
