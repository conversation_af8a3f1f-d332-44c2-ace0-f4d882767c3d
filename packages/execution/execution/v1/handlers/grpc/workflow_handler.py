"""
gRPC handler for workflow operations.
"""

from bson import ObjectId
from google.protobuf.empty_pb2 import Empty
import grpc

from common.interceptors.grpc.auth import get_and_validate_auth_payload
from common.log import error
from common.services.tenant_service import TenantService
from common.services.user_service import UserService
from common.utils.user_profile_mapper import user_to_profile_info
from common.utils.workflow_mapper import convert_workflow_model_to_proto
from orby.va.workflow_pb2 import (
    CreateWorkflowRequest,
    DeleteWorkflowRequest,
    GetWorkflowCreatorsResponse,
    GetWorkflowRequest,
    ListWorkflowsRequest,
    ListWorkflowsResponse,
    UpdateWorkflowRequest,
    Workflow,
)
from orby.va.workflow_service_pb2_grpc import WorkflowServiceServicer
from web_server.services.dto.workflow_dto import ListWorkflowsDto
from web_server.services.workflow_service import WorkflowService


class WorkflowHandler(WorkflowServiceServicer):
    """gRPC handler for workflow operations."""

    def __init__(self):
        self.workflow_service = WorkflowService()
        self.user_service = UserService()
        self.tenant_service = TenantService()

    async def ListWorkflows(
        self, request: ListWorkflowsRequest, context: grpc.ServicerContext
    ) -> ListWorkflowsResponse:
        """List workflows with filtering and pagination."""
        try:
            auth_payload = get_and_validate_auth_payload()
            tenant_info = await self.tenant_service.get_tenant_info(
                org_id=auth_payload.org_id
            )

            # Extract filtering parameters
            display_name_prefix = None
            creator_ids = None

            if request.filter:
                if request.filter.display_name_prefix:
                    display_name_prefix = request.filter.display_name_prefix
                if request.filter.creator_ids:
                    creator_ids = request.filter.creator_ids

            # Create DTO request - let service handle pagination validation
            list_request = ListWorkflowsDto(
                tenant_info=tenant_info,
                page_size=request.page_size,
                page_number=request.page_number,
                display_name_prefix=display_name_prefix,
                creator_ids=creator_ids,
            )

            # Get workflows with filtering
            workflows, total_count = await self.workflow_service.list_workflows(
                list_request
            )

            # Get creator info for all workflows
            creator_ids = [workflow.creator_id for workflow in workflows]
            users = await self.user_service.get_users_by_ids(creator_ids)

            # Convert to proto messages
            proto_workflows = []
            for workflow in workflows:
                user_info = users.get(workflow.creator_id)
                proto_workflow = convert_workflow_model_to_proto(workflow, user_info)
                proto_workflows.append(proto_workflow)

            return ListWorkflowsResponse(
                workflows=proto_workflows,
                total_size=total_count,
            )

        except ValueError as e:
            context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
            context.set_details(str(e))
        except Exception as e:
            error(f"Failed to list workflows: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details("Failed to list workflows")

    async def CreateWorkflow(
        self, request: CreateWorkflowRequest, context: grpc.ServicerContext
    ) -> Workflow:
        """Create a new workflow."""
        try:
            auth_payload = get_and_validate_auth_payload()
            tenant_info = await self.tenant_service.get_tenant_info(
                org_id=auth_payload.org_id
            )

            # Create workflow using the service
            workflow = await self.workflow_service.create_workflow(
                creator_id=auth_payload.user_id,
                tenant_info=tenant_info,
                thread_id=None,  # No thread_id until actual chat thread is created
                display_name="New Workflow",  # Default name, can be updated later
            )

            if workflow is None:
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details("Failed to create workflow")
                return

            # Get creator info (optional - workflow can be returned without creator details)
            user_info = None
            try:
                users = await self.user_service.get_users_by_ids(
                    [auth_payload.user_id]
                )
                user_info = users.get(auth_payload.user_id)
            except Exception as e:
                # Log the error but don't fail the request
                error(f"Failed to get user info for workflow creator {auth_payload.user_id}: {e}")

            return convert_workflow_model_to_proto(workflow, user_info)

        except Exception as e:
            error(f"Failed to create workflow: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details("Failed to create workflow")

    async def GetWorkflow(
        self, request: GetWorkflowRequest, context: grpc.ServicerContext
    ) -> Workflow:
        """Get a single workflow by ID."""
        # Validate workflow ID
        if not request.id or request.id.strip() == "":
            raise ValueError("Workflow ID is required")

        if not ObjectId.is_valid(request.id):
            raise ValueError(f"Invalid workflow ID format: '{request.id}'")

        workflow_id = ObjectId(request.id)

        try:
            auth_payload = get_and_validate_auth_payload()
            tenant_info = await self.tenant_service.get_tenant_info(
                org_id=auth_payload.org_id
            )

            # Get workflow
            workflow = await self.workflow_service.get_workflow_by_id(
                workflow_id=workflow_id,
                tenant_info=tenant_info,
            )

            if workflow is None:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Workflow with ID {request.id} not found")
                return

            # Get creator info
            users = await self.user_service.get_users_by_ids(
                [workflow.creator_id]
            )
            user_info = users.get(workflow.creator_id)

            return convert_workflow_model_to_proto(workflow, user_info)

        except ValueError as e:
            context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
            context.set_details(str(e))
        except Exception as e:
            error(f"Failed to get workflow: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details("Failed to get workflow")

    async def UpdateWorkflow(
        self, request: UpdateWorkflowRequest, context: grpc.ServicerContext
    ) -> Workflow:
        """Update a workflow."""
        # Validate workflow ID
        if not request.id:
            raise ValueError("Workflow ID is required")

        if not ObjectId.is_valid(request.id):
            raise ValueError(f"Invalid workflow ID format: {request.id}")

        workflow_id = ObjectId(request.id)

        try:
            auth_payload = get_and_validate_auth_payload()
            tenant_info = await self.tenant_service.get_tenant_info(
                org_id=auth_payload.org_id
            )

            # Prepare update parameters
            update_params = {
                "workflow_id": workflow_id,
                "tenant_info": tenant_info,
            }

            if request.display_name:
                update_params["display_name"] = request.display_name

            # Update workflow
            workflow = await self.workflow_service.update_workflow(
                **update_params
            )

            if workflow is None:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Workflow with ID {request.id} not found")
                return

            # Get creator info
            users = await self.user_service.get_users_by_ids(
                [workflow.creator_id]
            )
            user_info = users.get(workflow.creator_id)

            return convert_workflow_model_to_proto(workflow, user_info)

        except ValueError as e:
            context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
            context.set_details(str(e))
        except Exception as e:
            error(f"Failed to update workflow: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details("Failed to update workflow")

    async def DeleteWorkflow(
        self, request: DeleteWorkflowRequest, context: grpc.ServicerContext
    ) -> Empty:
        """Delete a workflow."""
        # Validate workflow ID
        if not request.id:
            raise ValueError("Workflow ID is required")

        if not ObjectId.is_valid(request.id):
            raise ValueError(f"Invalid workflow ID format: {request.id}")

        workflow_id = ObjectId(request.id)

        try:
            auth_payload = get_and_validate_auth_payload()
            tenant_info = await self.tenant_service.get_tenant_info(
                org_id=auth_payload.org_id
            )

            # Delete workflow
            success = await self.workflow_service.delete_workflow(
                workflow_id=workflow_id,
                tenant_info=tenant_info,
            )

            if not success:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Workflow with ID {request.id} not found")
                return

            return Empty()

        except ValueError as e:
            context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
            context.set_details(str(e))
        except Exception as e:
            error(f"Failed to delete workflow: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details("Failed to delete workflow")

    async def GetWorkflowCreators(
        self, request: Empty, context: grpc.ServicerContext
    ) -> GetWorkflowCreatorsResponse:
        """Get all workflow creators for dropdown (returns UserProfileInfo list)"""
        try:
            auth_payload = get_and_validate_auth_payload()
            tenant_info = await self.tenant_service.get_tenant_info(
                org_id=auth_payload.org_id
            )

            # Get all creator IDs
            creator_ids = await self.workflow_service.get_workflow_creators(
                tenant_info
            )

            if not creator_ids:
                # Return empty response if no creators
                return GetWorkflowCreatorsResponse(creators=[])

            # Get user info for all creators
            users = await self.user_service.get_users_by_ids(creator_ids)

            # Convert to UserProfileInfo list
            creators = []
            for creator_id in creator_ids:
                user = users.get(creator_id)
                if user:
                    user_profile = user_to_profile_info(user)
                    if user_profile:
                        creators.append(user_profile)

            return GetWorkflowCreatorsResponse(creators=creators)

        except Exception as e:
            error(f"Failed to get workflow creators: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details("Failed to get workflow creators")
