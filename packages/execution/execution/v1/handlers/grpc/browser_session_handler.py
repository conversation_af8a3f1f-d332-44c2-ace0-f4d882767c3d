import grpc
from orby.va.browser_session_pb2 import (
    CreateSessionRequest,
    CreateSessionResponse,
)
from orby.va.browser_session_service_pb2_grpc import (
    BrowserServiceServicer,
)

from common.log import error

from ...services.browser_service import BrowserService


class BrowserSessionHandler(BrowserServiceServicer):
    """gRPC handler for browser session management service"""

    def __init__(self):
        self.browser_service = BrowserService()

    def CreateSession(
        self, request: CreateSessionRequest, context: grpc.ServicerContext
    ) -> CreateSessionResponse:
        """Handle CreateSession gRPC call."""
        try:
            # For now, just pass through - later add domain model conversion
            created_session = self.browser_service.create_session(request)
            return created_session

        except ValueError as ve:
            error(f"Validation error during CreateSession: {ve}")
            context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
            context.set_details(str(ve))

        except grpc.RpcError as ge:
            error(f"gRPC error during CreateSession: {ge}")
            context.set_code(ge.code())
            context.set_details(ge.details())

        except Exception as e:
            error(f"Unhandled error during CreateSession: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details("Internal server error occurred")
