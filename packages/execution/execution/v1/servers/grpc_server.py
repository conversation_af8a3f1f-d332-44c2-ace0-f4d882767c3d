"""gRPC server implementation for execution service v1."""

import grpc
import grpc.aio
from grpc_reflection.v1alpha import reflection

from common.interceptors.grpc.auth import AuthInterceptor
from common.log import info
from orby.va.browser_session_service_pb2_grpc import (
    add_BrowserServiceServicer_to_server,
)
from orby.va.execution_management_service_pb2_grpc import (
    add_ExecutionManagementServiceServicer_to_server,
)
from orby.va.public.execution_service_pb2_grpc import (
    add_ExecutionServiceServicer_to_server,
)
from orby.va.public.user_utility_service_pb2_grpc import (
    add_UserUtilityServiceServicer_to_server,
)
from orby.va.workflow_service_pb2_grpc import (
    add_WorkflowServiceServicer_to_server,
)

from ..config.settings import config
from ..handlers.grpc.browser_session_handler import Browser<PERSON>essionHandler
from ..handlers.grpc.execution_handler import Execution<PERSON>andler
from ..handlers.grpc.user_utility_handler import User<PERSON><PERSON><PERSON>andler
from ..handlers.grpc.workflow_handler import WorkflowHandler


class GrpcServer:
    """gRPC server for execution service."""

    def __init__(self):
        self.server = None
        self.execution_handler = ExecutionHandler()
        self.browser_session_handler = BrowserSessionHandler()
        self.user_utility_handler = UserUtilityHandler()
        self.workflow_handler = WorkflowHandler()
        self.auth_interceptor = AuthInterceptor()

    async def start(self) -> None:
        """Start the gRPC server."""
        # Create server with auth interceptor
        self.server: grpc.aio.Server = grpc.aio.server(
            interceptors=[self.auth_interceptor],
        )

        # Add the service handler
        add_ExecutionServiceServicer_to_server(
            self.execution_handler, self.server
        )

        # Add the browser service handler
        add_BrowserServiceServicer_to_server(
            self.browser_session_handler, self.server
        )

        # Add the execution management service handler
        add_ExecutionManagementServiceServicer_to_server(
            self.execution_handler, self.server
        )

        # Add the user utility service handler
        add_UserUtilityServiceServicer_to_server(
            self.user_utility_handler, self.server
        )

        # Add the workflow service handler
        add_WorkflowServiceServicer_to_server(
            self.workflow_handler, self.server
        )
        # Explicitly declare service names
        service_names = (
            reflection.SERVICE_NAME,
            "orby.va.public.ExecutionService",
            "orby.va.BrowserService",
            "orby.va.public.UserUtilityService",
            "orby.va.ExecutionManagementService",
            "orby.va.WorkflowService",
        )
        reflection.enable_server_reflection(service_names, self.server)

        # Configure port
        listen_addr = f"[::]:{config.port}"
        info(f"gRPC server starting on {listen_addr}")
        self.server.add_insecure_port(listen_addr)

        # Start the server
        await self.server.start()
        info(f"gRPC server started and listening on {listen_addr}")

    async def stop(self, grace_period: int = 5) -> None:
        """Stop the gRPC server."""
        if self.server:
            info("Shutting down gRPC server...")
            await self.server.stop(grace_period)

    async def wait_for_termination(self) -> None:
        """Wait for server termination."""
        if self.server:
            await self.server.wait_for_termination()
