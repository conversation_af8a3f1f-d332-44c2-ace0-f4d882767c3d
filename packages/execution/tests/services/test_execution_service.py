"""Tests for ExecutionService."""

from datetime import UTC, datetime
from unittest.mock import AsyncMock, patch

from bson import ObjectId
from orby.va.execution_management_pb2 import (
    DeleteExecutionRequest,
    GetExecutionRequest,
    ListExecutionsRequest,
)
import pytest

from common.models.execution import (
    Execution as ExecutionModel,
)
from common.models.execution import (
    ExecutionBrowserService,
    ExecutionIdentifierType,
    ExecutionStatus,
    ExecutionTimestamps,
    ExecutionTriggeredBy,
    ExecutionUsers,
    ExecutionWorkflowContext,
)
from common.models.organization import Organization, TenantInfo, TenantType
from execution.v1.services.execution_service import ExecutionService


@pytest.fixture
def execution_service():
    """Create ExecutionService instance with mocked dependencies."""
    with patch(
        "execution.v1.services.execution_service.ExecutionRepository"
    ) as mock_execution_repository, patch(
        "execution.v1.services.execution_service.generate_signed_url",
        new_callable=AsyncMock
    ) as mock_generate_signed_url, patch(
        "execution.v1.services.execution_service.BrowserService"
    ) as mock_browser_service, patch(
        "execution.v1.services.execution_service.ReviewRepository"
    ) as mock_review_repository:
        mock_execution_repository.return_value = AsyncMock()
        mock_review_repository.return_value = AsyncMock()
        mock_browser_service.return_value = AsyncMock()
        
        # Mock the generate_signed_url function to return a signed URL
        mock_generate_signed_url.return_value = "https://signed.url.example.com/file"
        
        service = ExecutionService()
        return service


@pytest.fixture
def tenant_info():
    """Sample tenant info for testing."""
    org = Organization(
        _id=ObjectId("507f1f77bcf86cd799439011"),
        display_name="Test Org",
        tenant_type=TenantType.SHARED_TENANT,
    )
    return TenantInfo(org=org)


@pytest.fixture(scope="session")
def sample_execution():
    """Sample execution model for testing."""
    return ExecutionModel(
        _id=ObjectId("507f1f77bcf86cd799439012"),
        workflow_id=ObjectId("507f1f77bcf86cd799439013"),
        org_id=ObjectId("507f1f77bcf86cd799439011"),
        status=ExecutionStatus.PENDING,
        workflow_context=ExecutionWorkflowContext(commit_hash="abc123"),
        inputs={"key": "value"},
        triggered_by=ExecutionTriggeredBy(
            type=ExecutionIdentifierType.USER, identifier="user123"
        ),
        browser_service=ExecutionBrowserService(
            session_id="session123", context_id="context123"
        ),
        users=ExecutionUsers(cancelled_by="user456"),
        timestamps=ExecutionTimestamps(
            created_at=datetime(2023, 1, 1, 12, 0, 0, tzinfo=UTC),
            updated_at=datetime(2023, 1, 1, 12, 30, 0, tzinfo=UTC),
            started_at=datetime(2023, 1, 1, 12, 15, 0, tzinfo=UTC),
            finished_at=None,
            cancelled_at=None,
        ),
    )


@pytest.fixture(scope="session")
def sample_executions():
    """Sample list of execution models for testing."""
    return [
        ExecutionModel(
            _id=ObjectId("507f1f77bcf86cd799439012"),
            workflow_id=ObjectId("507f1f77bcf86cd799439013"),
            org_id=ObjectId("507f1f77bcf86cd799439011"),
            status=ExecutionStatus.PENDING,
        ),
        ExecutionModel(
            _id=ObjectId("507f1f77bcf86cd799439014"),
            workflow_id=ObjectId("507f1f77bcf86cd799439013"),
            org_id=ObjectId("507f1f77bcf86cd799439011"),
            status=ExecutionStatus.COMPLETED,
        ),
    ]


class TestGetExecution:
    """Test cases for get_execution method."""

    @pytest.mark.asyncio
    async def test_get_execution_success(
        self, execution_service, tenant_info, sample_execution
    ):
        """Test successful execution retrieval."""
        # Arrange
        execution_id = "507f1f77bcf86cd799439012"
        request = GetExecutionRequest(execution_id=execution_id)
        sample_execution.id = ObjectId(execution_id)
        execution_service.execution_repo.get_execution_by_id.return_value = (
            sample_execution
        )

        # Act
        result = await execution_service.get_execution(request, tenant_info)

        # Assert
        execution_service.execution_repo.get_execution_by_id.assert_called_once_with(
            ObjectId(execution_id), tenant_info
        )
        assert result.id == execution_id
        assert result.workflow_id == str(sample_execution.workflow_id)
        assert result.org_id == str(sample_execution.org_id)
        assert result.status == 1  # PENDING
        assert result.workflow_context.commit_hash == "abc123"
        assert result.inputs["key"] == "value"
        assert result.triggered_by.type == 1  # USER
        assert result.triggered_by.identifier == "user123"
        assert result.browser_service.session_id == "session123"
        assert result.browser_service.context_id == "context123"
        assert result.users.cancelled_by == "user456"

    @pytest.mark.asyncio
    async def test_get_execution_invalid_id(
        self, execution_service, tenant_info
    ):
        """Test get_execution with invalid execution ID."""
        # Arrange
        request = GetExecutionRequest(execution_id="invalid_id")

        # Act & Assert
        with pytest.raises(ValueError, match="Invalid execution ID"):
            await execution_service.get_execution(request, tenant_info)

    @pytest.mark.asyncio
    async def test_get_execution_not_found(
        self, execution_service, tenant_info
    ):
        """Test get_execution when execution is not found."""
        # Arrange
        execution_id = "507f1f77bcf86cd799439012"
        request = GetExecutionRequest(execution_id=execution_id)

        execution_service.execution_repo.get_execution_by_id.return_value = None

        # Act & Assert
        with pytest.raises(
            ValueError, match=f"Execution with ID {execution_id} not found"
        ):
            await execution_service.get_execution(request, tenant_info)


class TestListExecutions:
    """Test cases for list_executions method."""

    @pytest.mark.asyncio
    async def test_list_executions_success(
        self, execution_service, tenant_info, sample_executions
    ):
        """Test successful execution listing."""
        # Arrange
        workflow_id = "507f1f77bcf86cd799439013"
        request = ListExecutionsRequest(
            workflow_id=workflow_id,
            page_size=10,
            page_number=1,
        )

        execution_service.execution_repo.list_executions.return_value = (
            sample_executions
        )
        execution_service.execution_repo.count_executions.return_value = 2

        # Act
        result = await execution_service.list_executions(request, tenant_info)

        # Assert
        execution_service.execution_repo.list_executions.assert_called_once_with(
            ObjectId(workflow_id), 10, 0, tenant_info
        )
        execution_service.execution_repo.count_executions.assert_called_once_with(
            ObjectId(workflow_id), tenant_info
        )
        assert len(result.executions) == 2
        assert result.total_size == 2
        assert result.executions[0].id == str(sample_executions[0].id)
        assert result.executions[1].id == str(sample_executions[1].id)

    @pytest.mark.asyncio
    async def test_list_executions_invalid_workflow_id(
        self, execution_service, tenant_info
    ):
        """Test list_executions with invalid workflow ID."""
        # Arrange
        request = ListExecutionsRequest(workflow_id="invalid_id")

        # Act & Assert
        with pytest.raises(ValueError, match="Invalid workflow ID"):
            await execution_service.list_executions(request, tenant_info)

    @pytest.mark.asyncio
    async def test_list_executions_invalid_page_size(
        self, execution_service, tenant_info
    ):
        """Test list_executions with invalid page size."""
        # Arrange
        request = ListExecutionsRequest(
            workflow_id="507f1f77bcf86cd799439013",
            page_size=51,  # Greater than max allowed
            page_number=1,
        )

        # Act & Assert
        with pytest.raises(
            ValueError, match="Page size must be less than or equal to 50"
        ):
            await execution_service.list_executions(request, tenant_info)

    @pytest.mark.asyncio
    async def test_list_executions_invalid_page_number(
        self, execution_service, tenant_info
    ):
        """Test list_executions with invalid page number."""
        # Arrange
        request = ListExecutionsRequest(
            workflow_id="507f1f77bcf86cd799439013",
            page_size=10,
            page_number=0,  # Less than 1
        )

        # Act & Assert
        with pytest.raises(
            ValueError, match="Page number must be greater than 0"
        ):
            await execution_service.list_executions(request, tenant_info)

    @pytest.mark.asyncio
    async def test_list_executions_zero_page_size(
        self, execution_service, tenant_info, sample_executions
    ):
        """Test list_executions with zero page size (should default to 10)."""
        # Arrange
        workflow_id = "507f1f77bcf86cd799439013"
        request = ListExecutionsRequest(
            workflow_id=workflow_id,
            page_size=0,
            page_number=1,
        )

        execution_service.execution_repo.list_executions.return_value = (
            sample_executions
        )
        execution_service.execution_repo.count_executions.return_value = 2

        # Act
        result = await execution_service.list_executions(request, tenant_info)

        # Assert
        execution_service.execution_repo.list_executions.assert_called_once_with(
            ObjectId(workflow_id),
            10,
            0,
            tenant_info,  # Should default to 10
        )
        assert len(result.executions) == 2

    @pytest.mark.asyncio
    async def test_list_executions_pagination(
        self, execution_service, tenant_info, sample_executions
    ):
        """Test list_executions with pagination."""
        # Arrange
        workflow_id = "507f1f77bcf86cd799439013"
        request = ListExecutionsRequest(
            workflow_id=workflow_id,
            page_size=5,
            page_number=2,  # Second page
        )

        execution_service.execution_repo.list_executions.return_value = (
            sample_executions
        )
        execution_service.execution_repo.count_executions.return_value = 2

        # Act
        await execution_service.list_executions(request, tenant_info)

        # Assert
        # Should calculate offset as (page_number - 1) * page_size = (2-1) * 5 = 5
        execution_service.execution_repo.list_executions.assert_called_once_with(
            ObjectId(workflow_id), 5, 5, tenant_info
        )


class TestDeleteExecution:
    """Test cases for delete_execution method."""

    @pytest.mark.asyncio
    async def test_delete_execution_success(
        self, execution_service, tenant_info
    ):
        """Test successful execution deletion."""
        # Arrange
        execution_id = "507f1f77bcf86cd799439012"
        request = DeleteExecutionRequest(execution_id=execution_id)

        execution_service.execution_repo.delete_execution.return_value = True

        # Act
        result = await execution_service.delete_execution(request, tenant_info)

        # Assert
        execution_service.execution_repo.delete_execution.assert_called_once_with(
            ObjectId(execution_id), tenant_info
        )
        assert result is None

    @pytest.mark.asyncio
    async def test_delete_execution_invalid_id(
        self, execution_service, tenant_info
    ):
        """Test delete_execution with invalid execution ID."""
        # Arrange
        request = DeleteExecutionRequest(execution_id="invalid_id")

        # Act & Assert
        with pytest.raises(ValueError, match="Invalid execution ID"):
            await execution_service.delete_execution(request, tenant_info)

    @pytest.mark.asyncio
    async def test_delete_execution_not_found(
        self, execution_service, tenant_info
    ):
        """Test delete_execution when execution is not found."""
        # Arrange
        execution_id = "507f1f77bcf86cd799439012"
        request = DeleteExecutionRequest(execution_id=execution_id)

        execution_service.execution_repo.delete_execution.return_value = False

        # Act & Assert
        with pytest.raises(
            ValueError, match=f"Execution with ID {execution_id} not found"
        ):
            await execution_service.delete_execution(request, tenant_info)
