from datetime import UTC, datetime
from unittest.mock import patch

from bson import ObjectId
import pytest

from common.models.execution import Execution, ExecutionStatus
from common.models.organization import Organization, TenantInfo, TenantType
from common.models.task import (
    Task,
    TaskStatus,
    TaskTimestamps,
    TaskUsers,
)
from web_server.services.task_service import (
    CreateTaskRequest,
    DeleteTaskRequest,
    GetTaskByIdRequest,
    TaskService,
    UpdateTaskStatusRequest,
)


@pytest.fixture
def task_service():
    """TaskService instance with mocked dependencies."""
    return TaskService()


@pytest.fixture
def tenant_info():
    """TenantInfo fixture."""
    org = Organization(
        id=ObjectId("507f1f77bcf86cd799439012"),
        display_name="Test Organization",
        tenant_type=TenantType.SINGLE_TENANT,
    )
    return TenantInfo(org=org)


@pytest.fixture
def execution():
    """Execution fixture."""
    return Execution(
        id=ObjectId("507f1f77bcf86cd799439014"),
        org_id=ObjectId("507f1f77bcf86cd799439012"),
        workflow_id=ObjectId("507f1f77bcf86cd799439011"),
        status=ExecutionStatus.PENDING,
    )


@pytest.fixture
def task():
    """Task fixture."""
    return Task(
        id=ObjectId("507f1f77bcf86cd799439015"),
        org_id=ObjectId("507f1f77bcf86cd799439012"),
        workflow_id=ObjectId("507f1f77bcf86cd799439011"),
        identify_key="test_identify_key",
        execution_id=ObjectId("507f1f77bcf86cd799439014"),
        display_name="Test Task",
        description="Test Description",
        status=TaskStatus.PENDING,
        users=TaskUsers(
            creator_id=ObjectId("507f1f77bcf86cd799439013"),
        ),
        timestamps=TaskTimestamps(
            created_at=datetime.now(UTC),
            updated_at=datetime.now(UTC),
        ),
    )


class TestCreateTask:
    @pytest.mark.asyncio
    async def test_create_task_success(
        self, task_service, tenant_info, execution
    ):
        # Arrange
        identify_key = "test_identify_key"
        execution_id = ObjectId("507f1f77bcf86cd799439014")
        display_name = "Test Task"
        description = "Test Description"
        creator_id = ObjectId("507f1f77bcf86cd799439013")
        expected_task_id = ObjectId("507f1f77bcf86cd799439015")

        # Mock execution repository
        with patch.object(
            task_service._execution_repo, "find_one", return_value=execution
        ):
            # Mock task repository
            with patch.object(
                task_service._task_repo,
                "create_task",
                return_value=expected_task_id,
            ):
                # Act
                result = await task_service.create_task(
                    request=CreateTaskRequest(
                        identify_key=identify_key,
                        execution_id=execution_id,
                        display_name=display_name,
                        description=description,
                        creator_id=creator_id,
                        tenant_info=tenant_info,
                    ),
                )

                # Assert
                assert result == expected_task_id
                task_service._execution_repo.find_one.assert_called_once_with(
                    {"_id": execution_id, "org_id": tenant_info.org.id},
                    tenant_info,
                )
                task_service._task_repo.create_task.assert_called_once_with(
                    execution.workflow_id,
                    identify_key,
                    execution_id,
                    display_name,
                    description,
                    creator_id,
                    tenant_info,
                )

    @pytest.mark.asyncio
    async def test_create_task_missing_identify_key(
        self, task_service, tenant_info
    ):
        # Act & Assert
        with pytest.raises(ValueError, match="Identify key is required"):
            await task_service.create_task(
                request=CreateTaskRequest(
                    identify_key="",
                    execution_id=ObjectId(),
                    display_name="Test Task",
                    description="Test Description",
                    creator_id=ObjectId(),
                    tenant_info=tenant_info,
                ),
            )

    @pytest.mark.asyncio
    async def test_create_task_missing_execution_id(
        self, task_service, tenant_info
    ):
        # Act & Assert
        with pytest.raises(
            ValueError,
            match="validation error for CreateTaskRequest\nexecution_id",
        ):
            await task_service.create_task(
                request=CreateTaskRequest(
                    identify_key="test_key",
                    execution_id=None,
                    display_name="Test Task",
                    description="Test Description",
                    creator_id=ObjectId(),
                    tenant_info=tenant_info,
                ),
            )

    @pytest.mark.asyncio
    async def test_create_task_missing_display_name(
        self, task_service, tenant_info
    ):
        # Act & Assert
        with pytest.raises(ValueError, match="Display name is required"):
            await task_service.create_task(
                request=CreateTaskRequest(
                    identify_key="test_key",
                    execution_id=ObjectId(),
                    display_name="",
                    description="Test Description",
                    creator_id=ObjectId(),
                    tenant_info=tenant_info,
                ),
            )

    @pytest.mark.asyncio
    async def test_create_task_missing_creator_id(
        self, task_service, tenant_info
    ):
        # Act & Assert
        with pytest.raises(
            ValueError,
            match="validation error for CreateTaskRequest\ncreator_id",
        ):
            await task_service.create_task(
                request=CreateTaskRequest(
                    identify_key="test_key",
                    execution_id=ObjectId(),
                    display_name="Test Task",
                    description="Test Description",
                    creator_id=None,
                    tenant_info=tenant_info,
                ),
            )

    @pytest.mark.asyncio
    async def test_create_task_missing_tenant_info(self, task_service):
        # Act & Assert
        with pytest.raises(
            ValueError,
            match="validation error for CreateTaskRequest\ntenant_info",
        ):
            await task_service.create_task(
                request=CreateTaskRequest(
                    identify_key="test_key",
                    execution_id=ObjectId(),
                    display_name="Test Task",
                    description="Test Description",
                    creator_id=ObjectId(),
                    tenant_info=None,
                ),
            )

    @pytest.mark.asyncio
    async def test_create_task_execution_not_found(
        self, task_service, tenant_info
    ):
        # Arrange
        execution_id = ObjectId("507f1f77bcf86cd799439014")

        # Mock execution repository to return None
        with patch.object(
            task_service._execution_repo, "find_one", return_value=None
        ):
            # Act & Assert
            with pytest.raises(ValueError, match="Execution not found"):
                await task_service.create_task(
                    request=CreateTaskRequest(
                        identify_key="test_key",
                        execution_id=execution_id,
                        display_name="Test Task",
                        description="Test Description",
                        creator_id=ObjectId(),
                        tenant_info=tenant_info,
                    ),
                )

    @pytest.mark.asyncio
    async def test_create_task_creation_failed(
        self, task_service, tenant_info, execution
    ):
        # Arrange
        execution_id = ObjectId("507f1f77bcf86cd799439014")

        # Mock execution repository
        with patch.object(
            task_service._execution_repo, "find_one", return_value=execution
        ):
            # Mock task repository to return None
            with patch.object(
                task_service._task_repo, "create_task", return_value=None
            ):
                # Act & Assert
                with pytest.raises(ValueError, match="Failed to create task"):
                    (
                        await task_service.create_task(
                            request=CreateTaskRequest(
                                identify_key="test_key",
                                execution_id=execution_id,
                                display_name="Test Task",
                                description="Test Description",
                                creator_id=ObjectId(),
                                tenant_info=tenant_info,
                            ),
                        ),
                    )


class TestUpdateTaskStatus:
    @pytest.mark.asyncio
    async def test_update_task_status_success(
        self, task_service, tenant_info, task
    ):
        # Arrange
        task_id = ObjectId("507f1f77bcf86cd799439015")
        status = TaskStatus.COMPLETED

        # Mock task repository
        with patch.object(
            task_service._task_repo, "update_task", return_value=True
        ):
            with patch.object(
                task_service._task_repo, "get_by_id", return_value=task
            ):
                # Act
                result = await task_service.update_task_status(
                    request=UpdateTaskStatusRequest(
                        task_id=task_id,
                        status=status,
                        tenant_info=tenant_info,
                    ),
                )

                # Assert
                assert result == task
                task_service._task_repo.update_task.assert_called_once_with(
                    task_id, status, tenant_info
                )
                task_service._task_repo.get_by_id.assert_called_once_with(
                    task_id, tenant_info
                )

    @pytest.mark.asyncio
    async def test_update_task_status_missing_task_id(
        self, task_service, tenant_info
    ):
        # Act & Assert
        with pytest.raises(
            ValueError,
            match="validation error for UpdateTaskStatusRequest\ntask_id",
        ):
            await task_service.update_task_status(
                request=UpdateTaskStatusRequest(
                    task_id=None,
                    status=TaskStatus.COMPLETED,
                    tenant_info=tenant_info,
                ),
            )

    @pytest.mark.asyncio
    async def test_update_task_status_missing_status(
        self, task_service, tenant_info
    ):
        # Act & Assert
        with pytest.raises(
            ValueError,
            match="validation error for UpdateTaskStatusRequest\nstatus",
        ):
            await task_service.update_task_status(
                request=UpdateTaskStatusRequest(
                    task_id=ObjectId(),
                    status=None,
                    tenant_info=tenant_info,
                ),
            )

    @pytest.mark.asyncio
    async def test_update_task_status_invalid_status(
        self, task_service, tenant_info
    ):
        # Act & Assert
        with pytest.raises(
            ValueError,
            match="validation error for UpdateTaskStatusRequest\nstatus\n  Input should be 'pending', 'failed' or 'completed'",
        ):
            await task_service.update_task_status(
                request=UpdateTaskStatusRequest(
                    task_id=ObjectId(),
                    status="invalid_status",
                    tenant_info=tenant_info,
                ),
            )

    @pytest.mark.asyncio
    async def test_update_task_status_missing_tenant_info(self, task_service):
        # Act & Assert
        with pytest.raises(
            ValueError,
            match="validation error for UpdateTaskStatusRequest\ntenant_info",
        ):
            await task_service.update_task_status(
                request=UpdateTaskStatusRequest(
                    task_id=ObjectId(),
                    status=TaskStatus.COMPLETED,
                    tenant_info=None,
                ),
            )

    @pytest.mark.asyncio
    async def test_update_task_status_update_failed(
        self, task_service, tenant_info
    ):
        # Arrange
        task_id = ObjectId("507f1f77bcf86cd799439015")
        status = TaskStatus.COMPLETED

        # Mock task repository to return False
        with patch.object(
            task_service._task_repo, "update_task", return_value=False
        ):
            # Act & Assert
            with pytest.raises(ValueError, match="Failed to update task"):
                await task_service.update_task_status(
                    request=UpdateTaskStatusRequest(
                        task_id=task_id,
                        status=status,
                        tenant_info=tenant_info,
                    ),
                )

    @pytest.mark.asyncio
    async def test_update_task_status_task_not_found(
        self, task_service, tenant_info
    ):
        # Arrange
        task_id = ObjectId("507f1f77bcf86cd799439015")
        status = TaskStatus.COMPLETED

        # Mock task repository
        with patch.object(
            task_service._task_repo, "update_task", return_value=True
        ):
            with patch.object(
                task_service._task_repo, "get_by_id", return_value=None
            ):
                # Act & Assert
                with pytest.raises(ValueError, match="Task not found"):
                    await task_service.update_task_status(
                        request=UpdateTaskStatusRequest(
                            task_id=task_id,
                            status=status,
                            tenant_info=tenant_info,
                        ),
                    )


class TestDeleteTask:
    @pytest.mark.asyncio
    async def test_delete_task_success(self, task_service, tenant_info):
        # Arrange
        task_id = ObjectId("507f1f77bcf86cd799439015")

        # Mock task repository
        with patch.object(
            task_service._task_repo, "delete_task", return_value=True
        ):
            # Act
            result = await task_service.delete_task(
                request=DeleteTaskRequest(
                    task_id=task_id,
                    tenant_info=tenant_info,
                ),
            )

            # Assert
            assert result is None
            task_service._task_repo.delete_task.assert_called_once_with(
                task_id, tenant_info
            )

    @pytest.mark.asyncio
    async def test_delete_task_missing_task_id(self, task_service, tenant_info):
        # Act & Assert
        with pytest.raises(
            ValueError, match="validation error for DeleteTaskRequest\ntask_id"
        ):
            await task_service.delete_task(
                request=DeleteTaskRequest(
                    task_id=None,
                    tenant_info=tenant_info,
                ),
            )

    @pytest.mark.asyncio
    async def test_delete_task_missing_tenant_info(self, task_service):
        # Act & Assert
        with pytest.raises(
            ValueError,
            match="validation error for DeleteTaskRequest\ntenant_info",
        ):
            await task_service.delete_task(
                request=DeleteTaskRequest(
                    task_id=ObjectId(),
                    tenant_info=None,
                ),
            )

    @pytest.mark.asyncio
    async def test_delete_task_deletion_failed(self, task_service, tenant_info):
        # Arrange
        task_id = ObjectId("507f1f77bcf86cd799439015")

        # Mock task repository to return False
        with patch.object(
            task_service._task_repo, "delete_task", return_value=False
        ):
            # Act & Assert
            with pytest.raises(ValueError, match="Failed to delete task"):
                await task_service.delete_task(
                    request=DeleteTaskRequest(
                        task_id=task_id,
                        tenant_info=tenant_info,
                    ),
                )


class TestGetTaskById:
    @pytest.mark.asyncio
    async def test_get_task_by_id_success(
        self, task_service, tenant_info, task
    ):
        # Arrange
        task_id = ObjectId("507f1f77bcf86cd799439015")

        # Mock task repository
        with patch.object(
            task_service._task_repo, "get_task_by_id", return_value=task
        ):
            # Act
            result = await task_service.get_task_by_id(
                request=GetTaskByIdRequest(
                    task_id=task_id,
                    tenant_info=tenant_info,
                ),
            )

            # Assert
            assert result == task
            task_service._task_repo.get_task_by_id.assert_called_once_with(
                task_id, tenant_info
            )

    @pytest.mark.asyncio
    async def test_get_task_by_id_missing_task_id(
        self, task_service, tenant_info
    ):
        # Act & Assert
        with pytest.raises(
            ValueError, match="validation error for GetTaskByIdRequest\ntask_id"
        ):
            await task_service.get_task_by_id(
                request=GetTaskByIdRequest(
                    task_id=None,
                    tenant_info=tenant_info,
                ),
            )

    @pytest.mark.asyncio
    async def test_get_task_by_id_missing_tenant_info(self, task_service):
        # Act & Assert
        with pytest.raises(
            ValueError,
            match="validation error for GetTaskByIdRequest\ntenant_info",
        ):
            await task_service.get_task_by_id(
                request=GetTaskByIdRequest(
                    task_id=ObjectId(),
                    tenant_info=None,
                ),
            )

    @pytest.mark.asyncio
    async def test_get_task_by_id_task_not_found(
        self, task_service, tenant_info
    ):
        # Arrange
        task_id = ObjectId("507f1f77bcf86cd799439015")

        # Mock task repository to return None
        with patch.object(
            task_service._task_repo, "get_task_by_id", return_value=None
        ):
            # Act & Assert
            with pytest.raises(ValueError, match="Task not found"):
                await task_service.get_task_by_id(
                    request=GetTaskByIdRequest(
                        task_id=task_id,
                        tenant_info=tenant_info,
                    ),
                )
