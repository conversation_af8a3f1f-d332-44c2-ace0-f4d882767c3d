from datetime import UTC, datetime
from unittest.mock import AsyncMock, Mock, patch

from bson import ObjectId
import pytest

from common.models.organization import Organization, TenantInfo, TenantType
from common.models.task import (
    Task,
    TaskStatus,
    TaskTimestamps,
    TaskUsers,
)
from web_server.repositories.task_repository import TaskRepository


@pytest.fixture
def mock_collection():
    """Mock MongoDB collection."""
    return AsyncMock()


@pytest.fixture
def task_repo():
    """TaskRepository instance with mocked dependencies."""
    return TaskRepository()


@pytest.fixture
def tenant_info():
    """Sample tenant info for testing."""
    org = Organization(
        id=ObjectId("507f1f77bcf86cd799439012"),
        display_name="Test Org",
        tenant_type=TenantType.SHARED_TENANT,
    )
    return TenantInfo(org=org)


@pytest.mark.asyncio
async def test_create_task_success(task_repo, mock_collection, tenant_info):
    # Arrange
    workflow_id = ObjectId("507f1f77bcf86cd799439011")
    identify_key = "test_identify_key"
    execution_id = ObjectId("507f1f77bcf86cd799439014")
    display_name = "Test Task"
    description = "Test Description"
    creator_id = ObjectId("507f1f77bcf86cd799439013")
    expected_task_id = ObjectId("507f1f77bcf86cd799439011")

    mock_result = Mock()
    mock_result.inserted_id = expected_task_id
    mock_collection.insert_one = AsyncMock(return_value=mock_result)

    with patch.object(
        task_repo, "_get_collection", return_value=mock_collection
    ):
        # Act
        result = await task_repo.create_task(
            workflow_id,
            identify_key,
            execution_id,
            display_name,
            description,
            creator_id,
            tenant_info,
        )

        # Assert
        assert result == expected_task_id
        mock_collection.insert_one.assert_called_once()

        # Verify the insert data
        call_args = mock_collection.insert_one.call_args[0][0]
        assert call_args["org_id"] == tenant_info.org.id
        assert call_args["workflow_id"] == workflow_id
        assert call_args["identify_key"] == identify_key
        assert call_args["execution_id"] == execution_id
        assert call_args["display_name"] == display_name
        assert call_args["description"] == description
        assert call_args["status"] == TaskStatus.PENDING
        assert call_args["users"] == {"creator_id": creator_id}
        assert "timestamps" in call_args
        assert "created_at" in call_args["timestamps"]
        assert "updated_at" in call_args["timestamps"]


@pytest.mark.asyncio
async def test_create_task_failure(task_repo, mock_collection, tenant_info):
    # Arrange
    workflow_id = ObjectId("507f1f77bcf86cd799439011")
    identify_key = "test_identify_key"
    execution_id = ObjectId("507f1f77bcf86cd799439014")
    display_name = "Test Task"
    description = "Test Description"
    creator_id = ObjectId("507f1f77bcf86cd799439013")

    mock_collection.insert_one = AsyncMock(
        side_effect=Exception("Database error")
    )

    with patch.object(
        task_repo, "_get_collection", return_value=mock_collection
    ):
        # Act
        result = await task_repo.create_task(
            workflow_id,
            identify_key,
            execution_id,
            display_name,
            description,
            creator_id,
            tenant_info,
        )

        # Assert
        assert result is None


@pytest.mark.asyncio
async def test_update_task_success(task_repo, mock_collection, tenant_info):
    # Arrange
    task_id = ObjectId("507f1f77bcf86cd799439011")
    status = TaskStatus.COMPLETED

    mock_result = Mock()
    mock_result.modified_count = 1
    mock_collection.update_one = AsyncMock(return_value=mock_result)

    with patch.object(
        task_repo, "_get_collection", return_value=mock_collection
    ):
        # Act
        result = await task_repo.update_task(
            task_id,
            status,
            tenant_info,
        )

        # Assert
        assert result is True
        mock_collection.update_one.assert_called_once()

        # Verify the update call
        call_args = mock_collection.update_one.call_args
        assert call_args[0][0] == {"_id": task_id, "org_id": tenant_info.org.id}

        update_data = call_args[0][1]["$set"]
        assert update_data["status"] == status
        assert "timestamps.updated_at" in update_data


@pytest.mark.asyncio
async def test_update_task_failure(task_repo, mock_collection, tenant_info):
    # Arrange
    task_id = ObjectId("507f1f77bcf86cd799439011")
    status = TaskStatus.COMPLETED

    mock_collection.update_one = AsyncMock(
        side_effect=Exception("Database error")
    )

    with patch.object(
        task_repo, "_get_collection", return_value=mock_collection
    ):
        # Act
        result = await task_repo.update_task(
            task_id, status, tenant_info=tenant_info
        )

        # Assert
        assert result is False


@pytest.mark.asyncio
async def test_delete_task_success(task_repo, mock_collection, tenant_info):
    # Arrange
    task_id = ObjectId("507f1f77bcf86cd799439011")

    mock_result = Mock()
    mock_result.deleted_count = 1
    mock_collection.delete_one = AsyncMock(return_value=mock_result)

    with patch.object(
        task_repo, "_get_collection", return_value=mock_collection
    ):
        # Act
        result = await task_repo.delete_task(task_id, tenant_info)

        # Assert
        assert result is True
        mock_collection.delete_one.assert_called_once()

        # Verify the delete call
        call_args = mock_collection.delete_one.call_args[0][0]
        assert call_args["_id"] == task_id
        assert call_args["org_id"] == tenant_info.org.id


@pytest.mark.asyncio
async def test_delete_task_failure(task_repo, mock_collection, tenant_info):
    # Arrange
    task_id = ObjectId("507f1f77bcf86cd799439011")

    mock_collection.delete_one = AsyncMock(
        side_effect=Exception("Database error")
    )

    with patch.object(
        task_repo, "_get_collection", return_value=mock_collection
    ):
        # Act
        result = await task_repo.delete_task(task_id, tenant_info)

        # Assert
        assert result is False


@pytest.mark.asyncio
async def test_get_task_by_id_success(task_repo, tenant_info):
    # Arrange
    task_id = ObjectId("507f1f77bcf86cd799439011")
    expected_task = Task(
        id=task_id,
        org_id=ObjectId("507f1f77bcf86cd799439012"),
        workflow_id=ObjectId("507f1f77bcf86cd799439011"),
        identify_key="test_identify_key",
        execution_id=ObjectId("507f1f77bcf86cd799439014"),
        display_name="Test Task",
        description="Test Description",
        status=TaskStatus.PENDING,
        users=TaskUsers(
            creator_id=ObjectId("507f1f77bcf86cd799439013"),
        ),
        timestamps=TaskTimestamps(
            created_at=datetime.now(UTC),
            updated_at=datetime.now(UTC),
        ),
    )

    with patch.object(
        task_repo, "find_one", return_value=expected_task
    ) as mock_get_by_id:
        # Act
        result = await task_repo.get_task_by_id(task_id, tenant_info)

        # Assert
        assert result == expected_task
        mock_get_by_id.assert_called_once_with(
            {"_id": task_id, "org_id": tenant_info.org.id}, tenant_info
        )


@pytest.mark.asyncio
async def test_get_task_by_id_failure(task_repo, tenant_info):
    # Arrange
    task_id = ObjectId("507f1f77bcf86cd799439011")

    with patch.object(
        task_repo, "get_by_id", side_effect=Exception("Database error")
    ):
        # Act
        result = await task_repo.get_task_by_id(task_id, tenant_info)

        # Assert
        assert result is None
