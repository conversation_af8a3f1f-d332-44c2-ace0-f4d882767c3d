from bson import ObjectId
from pydantic import BaseModel, ConfigDict

from common.models.organization import TenantInfo
from common.models.task import Task, TaskStatus

from ..repositories.execution_repository import ExecutionRepository
from ..repositories.task_repository import TaskRepository


class CreateTaskRequest(BaseModel):
    model_config = ConfigDict(arbitrary_types_allowed=True)

    tenant_info: TenantInfo
    identify_key: str
    execution_id: ObjectId
    display_name: str
    description: str
    creator_id: ObjectId


class UpdateTaskStatusRequest(BaseModel):
    model_config = ConfigDict(arbitrary_types_allowed=True)

    tenant_info: TenantInfo
    task_id: ObjectId
    status: TaskStatus


class DeleteTaskRequest(BaseModel):
    model_config = ConfigDict(arbitrary_types_allowed=True)

    tenant_info: TenantInfo
    task_id: ObjectId


class GetTaskByIdRequest(BaseModel):
    model_config = ConfigDict(arbitrary_types_allowed=True)

    tenant_info: TenantInfo
    task_id: ObjectId


class TaskService:
    def __init__(self):
        self._task_repo = TaskRepository()
        self._execution_repo = ExecutionRepository()

    async def create_task(
        self,
        request: CreateTaskRequest,
    ) -> Task:
        if not request.identify_key:
            raise ValueError("Identify key is required")
        if not request.display_name:
            raise ValueError("Display name is required")

        execution = await self._execution_repo.find_one(
            {"_id": request.execution_id, "org_id": request.tenant_info.org.id},
            request.tenant_info,
        )
        if not execution:
            raise ValueError("Execution not found")

        task = await self._task_repo.create_task(
            execution.workflow_id,
            request.identify_key,
            request.execution_id,
            request.display_name,
            request.description,
            request.creator_id,
            request.tenant_info,
        )
        if not task:
            raise ValueError("Failed to create task")
        return task

    async def update_task_status(
        self,
        request: UpdateTaskStatusRequest,
    ) -> Task:
        result = await self._task_repo.update_task(
            request.task_id, request.status, request.tenant_info
        )
        if not result:
            raise ValueError("Failed to update task")

        task = await self._task_repo.get_by_id(
            request.task_id, request.tenant_info
        )
        if not task:
            raise ValueError("Task not found")
        return task

    async def delete_task(
        self,
        request: DeleteTaskRequest,
    ) -> None:
        result = await self._task_repo.delete_task(
            request.task_id,
            request.tenant_info,
        )
        if not result:
            raise ValueError("Failed to delete task")
        return None

    async def get_task_by_id(
        self,
        request: GetTaskByIdRequest,
    ) -> Task:
        task = await self._task_repo.get_task_by_id(
            request.task_id, request.tenant_info
        )
        if not task:
            raise ValueError("Task not found")
        return task
