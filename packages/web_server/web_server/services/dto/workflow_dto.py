"""
Data Transfer Objects for Workflow Service operations.
"""

from bson import ObjectId
from pydantic import BaseModel, ConfigDict, Field, field_validator

from common.models.organization import TenantInfo


class ListWorkflowsDto(BaseModel):
    """DTO for listing workflows with filtering and pagination."""

    model_config = ConfigDict(arbitrary_types_allowed=True)

    tenant_info: TenantInfo
    page_size: int = Field(default=10, ge=1, le=20)
    page_number: int = Field(default=1, ge=1)
    display_name_prefix: str | None = None
    creator_ids: list[ObjectId] | None = None

    @field_validator("creator_ids", mode="before")
    @classmethod
    def validate_creator_ids(cls, v):
        if v is None:
            return v
        if isinstance(v, list):
            return [
                ObjectId(item) if isinstance(item, str) else item for item in v
            ]
        return v
