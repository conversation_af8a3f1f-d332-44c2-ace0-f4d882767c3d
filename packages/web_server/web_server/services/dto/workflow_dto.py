"""
Data Transfer Objects for Workflow Service operations.
"""

from bson import ObjectId
from pydantic import BaseModel, ConfigDict, Field

from common.models.organization import TenantInfo


class ListWorkflowsDto(BaseModel):
    """DTO for listing workflows with filtering and pagination."""

    model_config = ConfigDict(arbitrary_types_allowed=True)

    tenant_info: TenantInfo
    page_size: int = Field(default=10, ge=1, le=20)
    page_number: int = Field(default=1, ge=1)
    display_name_prefix: str | None = None
    creator_ids: list[ObjectId] | None = None
