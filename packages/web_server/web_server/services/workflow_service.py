"""Business service for workflow operations."""

from bson import ObjectId

from common.log import error
from common.models.organization import TenantInfo
from common.models.workflow import (
    Workflow,
    WorkflowGeneratedFiles,
    WorkflowGitRepository,
    WorkflowStatus,
)

from ..repositories.workflow_repository import WorkflowRepository
from .dto.workflow_dto import ListWorkflowsDto


class WorkflowService:
    def __init__(self):
        """Initialize workflow service"""
        self._workflow_repo = WorkflowRepository()

    async def create_workflow(
        self,
        creator_id: ObjectId,
        tenant_info: TenantInfo,
        thread_id: str,
        display_name: str | None = None,
    ) -> Workflow | None:
        """Create a new workflow for chat session tracking"""
        try:
            workflow_id = await self._workflow_repo.create_workflow(
                creator_id=creator_id,
                tenant_info=tenant_info,
                thread_id=thread_id,
                display_name=display_name,
            )
            if not workflow_id:
                return None

            # Return the created workflow
            return await self._workflow_repo.get_workflow_by_id(
                workflow_id, tenant_info
            )

        except Exception as e:
            error(f"Error creating workflow: {e}")
            raise e

    async def update_workflow(
        self,
        workflow_id: ObjectId,
        tenant_info: TenantInfo,
        display_name: str | None = None,
        git_repository: WorkflowGitRepository | None = None,
        generated_files: WorkflowGeneratedFiles | None = None,
        status: WorkflowStatus | None = None,
        bb_context_id: str | None = None,
    ) -> Workflow | None:
        """Update workflow with final data when complete"""
        try:
            success = await self._workflow_repo.update_workflow(
                workflow_id=workflow_id,
                tenant_info=tenant_info,
                display_name=display_name,
                git_repository=git_repository,
                generated_files=generated_files,
                status=status,
                bb_context_id=bb_context_id,
            )

            if not success:
                return None

            # Return updated workflow
            return await self._workflow_repo.get_workflow_by_id(
                workflow_id, tenant_info
            )

        except Exception as e:
            error(f"Error updating workflow: {e}")
            raise e

    async def list_workflows(
        self, request: ListWorkflowsDto
    ) -> tuple[list[Workflow], int]:
        """List workflows with pagination and filtering"""
        try:
            # Calculate offset from page number (validation handled by Pydantic)
            offset = (request.page_number - 1) * request.page_size

            # Get workflows from repository with filtering
            workflows = await self._workflow_repo.list_workflows(
                tenant_info=request.tenant_info,
                limit=request.page_size,
                offset=offset,
                display_name_prefix=request.display_name_prefix,
                creator_ids=request.creator_ids,
            )

            # Get total count for pagination with same filters
            total_count = await self._workflow_repo.count_workflows(
                tenant_info=request.tenant_info,
                display_name_prefix=request.display_name_prefix,
                creator_ids=request.creator_ids,
            )

            return workflows, total_count

        except Exception as e:
            error(f"Error listing workflows: {e}")
            raise e

    async def delete_workflow(
        self, workflow_id: ObjectId, tenant_info: TenantInfo
    ) -> bool:
        """Delete a workflow"""
        try:
            return await self._workflow_repo.delete_workflow(
                workflow_id=workflow_id,
                tenant_info=tenant_info,
            )

        except Exception as e:
            error(f"Error deleting workflow: {e}")
            raise e

    async def get_workflow_by_id(
        self, workflow_id: ObjectId, tenant_info: TenantInfo
    ) -> Workflow | None:
        """Get workflow by ID"""
        try:
            return await self._workflow_repo.get_workflow_by_id(
                workflow_id, tenant_info
            )

        except Exception as e:
            error(f"Error getting workflow: {e}")
            raise e

    async def get_workflow_by_thread_id(
        self, thread_id: str, tenant_info: TenantInfo | None = None
    ) -> Workflow | None:
        """Get workflow by thread ID"""
        try:
            return await self._workflow_repo.get_workflow_by_thread_id(
                thread_id, tenant_info
            )

        except Exception as e:
            error(f"Error getting workflow by thread ID: {e}")
            raise e

    async def get_workflow_creators(
        self, tenant_info: TenantInfo
    ) -> list[ObjectId]:
        """Get all unique creator IDs for workflows in the tenant"""
        try:
            return await self._workflow_repo.get_workflow_creators(tenant_info)
        except Exception as e:
            error(f"Error getting workflow creators: {e}")
            raise e
