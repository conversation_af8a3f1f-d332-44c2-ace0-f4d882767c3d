"""
Chainlit Chat Interface for Multi-Agent System
Main application entry point that coordinates all chat components
"""

import os
import sys

import chainlit as cl

from agent.claude_code_agent import (
    get_claude_cli_agent,
)
from common.log import info
from web_server.chat.commands import register_commands

# Import chat components
from web_server.chat.initialization import (
    end_chat_session,
    handle_chat_resume,
    session_manager,
    start_chat_session,
)
from web_server.chat.message_handler import handle_message
from web_server.core.config import server_config
from web_server.message_processing.factory import create_message_processor

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Initialize LangSmith tracing if API key is available
if server_config.langsmith_api_key:
    from agent.langsmith_config import setup_langsmith_environment

    setup_langsmith_environment(server_config)
    info("LangSmith tracing enabled")

# Initialize core components
info("Initializing core components...")

# Message processor factory (using session manager from initialization module)
message_processor = create_message_processor(
    session_manager, get_claude_cli_agent()
)

info("Core components initialized successfully")


@cl.on_chat_start
async def start():
    """
    Initialize a new chat session.
    Sets up session state and starts runtime initialization in the background.
    """
    info("Starting new chat session")
    await register_commands()
    await start_chat_session()


@cl.on_message
async def on_message(message: cl.Message):
    """
    Handle incoming messages from the user.
    Processes messages through the message processing pipeline.
    """
    await handle_message(message, message_processor)


@cl.on_chat_end
async def end():
    """
    Clean up when chat session ends.
    Stops runtime, cleans up files, and cancels background tasks.
    """
    info("Ending chat session")
    await end_chat_session()


@cl.on_chat_resume
async def on_chat_resume(thread):
    """
    Handle chat session resume.
    Reinitializes runtime environment and restores session state.
    """
    info("Resuming chat session")
    await register_commands()
    await handle_chat_resume(thread)
