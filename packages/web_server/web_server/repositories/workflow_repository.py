"""Repository for workflow database operations."""

from datetime import UTC, datetime

from bson import ObjectId

from common.log import error
from common.models.organization import TenantInfo
from common.models.workflow import (
    Workflow,
    WorkflowGeneratedFiles,
    WorkflowGitRepository,
    WorkflowStatus,
)
from common.repository.base import BaseRepository


class WorkflowRepository(BaseRepository[Workflow]):
    def __init__(self):
        super().__init__(Workflow)

    async def create_workflow(
        self,
        creator_id: ObjectId,
        tenant_info: TenantInfo,
        thread_id: str,
        display_name: str | None = None,
    ) -> ObjectId | None:
        """Create a new workflow and return its ID"""
        workflow = Workflow(
            org_id=tenant_info.org.id,
            creator_id=creator_id,
            created_at=datetime.now(UTC),
            updated_at=datetime.now(UTC),
            thread_id=thread_id,
            display_name=display_name,
        )

        try:
            collection = await self._get_collection(tenant_info)
            # Use model serialization with context-aware ObjectId handling
            workflow_dict = workflow.model_dump(by_alias=True, mode="python")
            result = await collection.insert_one(workflow_dict)
            return result.inserted_id
        except Exception as e:
            error(f"Failed to create workflow: {e}")
            return None

    async def update_workflow(
        self,
        workflow_id: ObjectId,
        tenant_info: TenantInfo,
        display_name: str | None = None,
        git_repository: WorkflowGitRepository | None = None,
        generated_files: WorkflowGeneratedFiles | None = None,
        status: WorkflowStatus | None = None,
        bb_context_id: str | None = None,
    ) -> bool:
        """Update workflow with final data"""
        update_data = {
            "updated_at": datetime.now(UTC),
        }
        if display_name is not None:
            update_data["display_name"] = display_name

        if git_repository:
            update_data["git_repository"] = git_repository.model_dump(
                mode="python"
            )

        if generated_files:
            update_data["generated_files"] = generated_files.model_dump(
                mode="python"
            )

        if status:
            update_data["status"] = status.value

        if bb_context_id:
            update_data["bb_context_id"] = bb_context_id

        try:
            collection = await self._get_collection(tenant_info)
            result = await collection.update_one(
                {"_id": workflow_id, "org_id": tenant_info.org.id},
                {"$set": update_data},
            )
            return result.modified_count > 0
        except Exception as e:
            error(f"Failed to update workflow: {e}")
            return False

    async def get_workflow_by_thread_id(
        self, thread_id: str, tenant_info: TenantInfo | None = None
    ) -> Workflow | None:
        try:
            collection = await self._get_collection(tenant_info)
            document = await collection.find_one({"thread_id": thread_id})
            return Workflow(**document) if document else None
        except Exception as e:
            error(f"Failed to get workflow by thread id: {e}")
            return None

    async def get_workflow_by_id(
        self, workflow_id: ObjectId, tenant_info: TenantInfo | None = None
    ) -> Workflow | None:
        """Get workflow by ID"""
        try:
            collection = await self._get_collection(tenant_info)
            document = await collection.find_one(
                {"_id": workflow_id, "org_id": tenant_info.org.id}
            )

            if document:
                return Workflow(**document)
            return None
        except Exception as e:
            error(f"Failed to get workflow: {e}")
            return None

    async def list_workflows(
        self,
        tenant_info: TenantInfo,
        limit: int = 50,
        offset: int = 0,
        display_name_prefix: str | None = None,
        creator_ids: list[ObjectId] | None = None,
    ) -> list[Workflow]:
        """List workflows for an organization with pagination and filtering"""
        try:
            collection = await self._get_collection(tenant_info)

            # Build query filter
            query_filter = {"org_id": tenant_info.org.id}

            # Add display name prefix filter (case-insensitive)
            if display_name_prefix:
                query_filter["display_name"] = {
                    "$regex": f"^{display_name_prefix}",
                    "$options": "i",
                }

            # Add creator IDs filter
            if creator_ids:
                query_filter["creator_id"] = {"$in": creator_ids}

            cursor = (
                collection.find(query_filter)
                .skip(offset)
                .limit(limit)
                .sort("created_at", -1)
            )

            docs = await cursor.to_list(length=limit)
            workflows = [Workflow(**doc) for doc in docs]
            return workflows
        except Exception as e:
            error(f"Failed to list workflows: {e}")
            return []

    async def count_workflows(
        self,
        tenant_info: TenantInfo,
        display_name_prefix: str | None = None,
        creator_ids: list[ObjectId] | None = None,
    ) -> int:
        """Count total workflows for an organization with filtering"""
        try:
            collection = await self._get_collection(tenant_info)

            # Build query filter (same as in list_workflows)
            query_filter = {"org_id": tenant_info.org.id}

            # Add display name prefix filter (case-insensitive)
            if display_name_prefix:
                query_filter["display_name"] = {
                    "$regex": f"^{display_name_prefix}",
                    "$options": "i",
                }

            # Add creator IDs filter
            if creator_ids:
                query_filter["creator_id"] = {"$in": creator_ids}

            count = await collection.count_documents(query_filter)
            return count
        except Exception as e:
            error(f"Failed to count workflows: {e}")
            return 0

    async def delete_workflow(
        self, workflow_id: ObjectId, tenant_info: TenantInfo | None = None
    ) -> bool:
        """Delete workflow by ID"""
        try:
            collection = await self._get_collection(tenant_info)
            result = await collection.delete_one(
                {"_id": workflow_id, "org_id": tenant_info.org.id}
            )
            return result.deleted_count > 0
        except Exception as e:
            error(f"Failed to delete workflow: {e}")
            return False

    async def get_workflow_creators(
        self, tenant_info: TenantInfo
    ) -> list[ObjectId]:
        """Get all unique creator IDs for workflows in the tenant"""
        try:
            collection = await self._get_collection(tenant_info)
            # Use distinct to get unique creator IDs
            creator_ids = await collection.distinct(
                "creator_id", {"org_id": tenant_info.org.id}
            )
            return creator_ids
        except Exception as e:
            error(f"Failed to get workflow creators: {e}")
            return []
