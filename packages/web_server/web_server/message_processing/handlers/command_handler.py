"""
New command handler that uses Chainlit's official command system
"""

from web_server.chat.commands import handle_command

from ..base import MessageContext, MessageHandler, ProcessingResult


class ChainlitCommandHandler(MessageHandler):
    """Handles Chainlit commands via msg.command property"""

    def __init__(self):
        """Initialize the command handler."""
        super().__init__()

    async def can_handle(self, context: MessageContext) -> bool:
        """Check if this is a command message"""
        return context.message.command is not None

    async def handle(self, context: MessageContext) -> ProcessingResult:
        """Handle command messages"""
        try:
            command_id = context.message.command
            if command_id:
                handled = await handle_command(command_id)
                if handled:
                    return ProcessingResult.handled()

            return ProcessingResult.continue_processing()
        except Exception as e:
            return ProcessingResult.error(
                e, f"Error handling command: {str(e)}"
            )
