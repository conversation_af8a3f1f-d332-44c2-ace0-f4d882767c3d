"""
Handler for messages in single-agent mode.
"""

import asyncio
import json

from bson import ObjectId
import chainlit as cl
from langchain_core.messages import HumanMessage
from langchain_core.runnables import RunnableConfig

from agent.claude_code_agent import ClaudeC<PERSON>Agent
from common.constants.e2b_constants import REPO_NAME
from common.log.log import error, info, warn
from common.models.workflow import (
    WorkflowGeneratedFiles,
    WorkflowGitRepository,
    WorkflowStatus,
)
from common.sandbox.log_streamer import FilterType
from web_server.constants.chat import WindowMessageType
from web_server.constants.env import GENERATED_MANIFEST_FILE_NAME
from web_server.services.user_file_service import UserFileService
from web_server.services.workflow_service import WorkflowService
from web_server.session.state_manager import AppMode, Workflow
from web_server.utils.execution_log_parser import parse_sdk_review_event

from ..base import Message<PERSON>ontext, MessageHandler, ProcessingResult


class SingleAgentModeHandler(MessageHandler):
    """Handles messages in single-agent mode"""

    def __init__(self, single_agent: ClaudeCliAgent):
        """Initialize the single-agent mode handler."""
        super().__init__()
        self.single_agent_graph = single_agent.graph

    async def can_handle(self, context: MessageContext) -> bool:
        return self.state_manager.get_app_mode() == AppMode.BUILD

    async def handle(self, context: MessageContext) -> ProcessingResult:
        """Handle single-agent mode messages"""
        # If no workflow exists for this session, create an initial workflow
        existing_workflow = await self.state_manager.get_workflow(
            cl.context.session.thread_id
        )
        if not existing_workflow:
            await self._create_initial_workflow(context)

        execution_id = f"test-{cl.context.session.thread_id}"
        # Create a step for execution logs (auto-starts when entered)
        async with cl.Step(
            name="Orby is thinking...", type="run", default_open=True
        ) as step:
            log_content = []

            # Get the current event loop for thread-safe scheduling
            main_loop = asyncio.get_running_loop()

            def on_log_received(log_entry):
                """Callback for real-time log streaming into step"""
                # Only handle stdout logs (no stderr as requested)
                if log_entry.stream_type == "stdout":
                    content = log_entry.content
                    parsed_log = parse_sdk_review_event(
                        log_entry.content, execution_id
                    )
                    if parsed_log:
                        content = (
                            f"**{parsed_log.get('user_message', content)}**"
                        )
                        web_app_message = json.dumps(
                            parsed_log, ensure_ascii=False
                        )

                        # If the log entry is a review request, send it as a window message
                        async def send_window_message_with_error_handling():
                            """Send the log line to the browser window via state manager"""
                            try:
                                await self.state_manager.send_window_message(
                                    web_app_message
                                )
                            except Exception as e:
                                warn(f"Error sending window message: {e}")

                        try:
                            asyncio.run_coroutine_threadsafe(
                                send_window_message_with_error_handling(),
                                main_loop,
                            )
                        except Exception as e:
                            warn(f"Error scheduling task: {e}")
                    log_content.append(content)
                    # Stream the new log content in real-time

                    # Use thread-safe scheduling since callback is called from different thread
                    async def stream_with_error_handling():
                        try:
                            await step.stream_token(content + "\n")
                        except Exception as e:
                            warn(f"Error in stream_token: {e}")

                    try:
                        # Schedule coroutine in main event loop from different thread
                        asyncio.run_coroutine_threadsafe(
                            stream_with_error_handling(), main_loop
                        )
                    except Exception as e:
                        warn(f"Error scheduling task: {e}")

            # Start log streaming with callback and Claude filtering
            if context.log_streamer:
                context.log_streamer.start(
                    on_log_callback=on_log_received,
                    filter_type=FilterType.CLAUDE,
                    execution_id=execution_id,
                )

            # Process the main message
            # Note we dont process langchain messages here, we only stream logs from the runtime
            await self._process_single_agent_message(context, execution_id)

            # Stop log streaming
            if context.log_streamer:
                context.log_streamer.stop()

            # Set final step output
            if log_content:
                step.output = "\n".join(log_content)
            else:
                step.output = "No logs captured"

        # Check and try to persist the workflow if it is complete
        await self._process_workflow_completion(context)
        return ProcessingResult.handled()

    async def _create_initial_workflow(self, context: MessageContext) -> None:
        """Create an initial workflow entry at the beginning of the session."""
        try:
            tenant_info = await self._get_tenant_info()
            user_id = ObjectId(cl.context.session.user.id)
            workflow_service = WorkflowService()

            # Use the first user message (truncated) as the initial display name
            initial_display_name = (
                context.content[:75] if context.content else "New Workflow"
            )

            created_workflow = await workflow_service.create_workflow(
                creator_id=user_id,
                tenant_info=tenant_info,
                display_name=initial_display_name,
                thread_id=cl.context.session.thread_id,
            )

            if created_workflow:
                # Create initial workflow view model and set UI state
                initial_workflow = Workflow(
                    name=initial_display_name,
                    mongo_id=created_workflow.id,
                    thread_id=cl.context.session.thread_id,
                    is_executable=False,  # Not executable until completion
                    commit_hash="",  # No commit hash yet
                    bb_context_id=context.runtime.browser_session.context_id
                    if context.runtime.browser_session
                    else "",
                )
                await self.state_manager.set_workflow(initial_workflow)

                # TODO: clean up logging in the future
                info(f"Initial workflow created with ID: {created_workflow.id}")
            else:
                warn("Failed to create initial workflow entry.")

        except Exception as e:
            error(f"Error creating initial workflow: {e}")

    async def _process_single_agent_message(
        self, context: MessageContext, test_execution_id: str
    ) -> str:
        """Process a single-agent mode message"""
        existing_workflow = await self.state_manager.get_workflow(
            cl.context.session.thread_id
        )
        is_first_message = existing_workflow is None

        user_content = context.content
        config = {"thread_id": context.session_id}
        runnable_config = RunnableConfig(configurable=config)
        current_state = {
            "runtime": context.runtime,
            "is_first_message": is_first_message,
            "session_id": context.session_id,
            "messages": [HumanMessage(content=user_content)],
            "attachments": [
                attachment.name
                for attachment in self.state_manager.get_attachments()
            ],
            "execution_id": test_execution_id,
        }

        result = ""
        async for mode, stream_chunk in self.single_agent_graph.astream(
            current_state,
            config=runnable_config,
            stream_mode=["values"],
        ):
            if mode == "values":
                result = stream_chunk

        return result

    async def _process_workflow_completion(
        self, context: MessageContext
    ) -> None:
        """Process the workflow completion"""
        # Check if the workflow is complete
        if not context.runtime:
            return
        manifest_bytes = await self._check_for_workflow_completion(context)
        if manifest_bytes:
            commit_hash = await context.runtime.push_workspace()
            # Parse manifest json to get workflow name
            manifest_json = json.loads(manifest_bytes)
            workflow_name = manifest_json.get("name", "Unknown Workflow Name")

            # Get tenant info and user info from chainlit context
            tenant_info = await self._get_tenant_info()
            user_id = ObjectId(cl.context.session.user.id)
            workflow_service = WorkflowService()
            user_file_service = UserFileService()

            # Retrieve the workflow object that was created and saved in the session state
            session_workflow = await self.state_manager.get_workflow(
                cl.context.session.thread_id
            )
            if not session_workflow:
                await cl.Message(
                    content=f"⚠️ Failed to update workflow **{workflow_name}**. Please try again."
                ).send()
                return

            workflow_id = session_workflow.mongo_id

            # Save manifest file to user_files
            manifest_file_id = await user_file_service.create_user_file(
                content=manifest_bytes,
                filename="manifest.json",
                file_type="manifest",
                workflow_id=workflow_id,
                creator_id=user_id,
                tenant_info=tenant_info,
            )

            # Save SOP file if it exists
            sop_file_id = None
            sop_bytes = await self._check_for_sop_completion(context)
            if sop_bytes:
                sop_file_id = await user_file_service.create_user_file(
                    content=sop_bytes,
                    filename="SOP.md",
                    file_type="sop",
                    workflow_id=workflow_id,
                    creator_id=user_id,
                    tenant_info=tenant_info,
                )

            # Create generated files reference
            generated_files = WorkflowGeneratedFiles(
                sop_file_id=sop_file_id,
                manifest_file_id=manifest_file_id,
            )

            # Update workflow with git repository and generated files
            git_repository = WorkflowGitRepository(
                repo_url=REPO_NAME,
                commit_hash=commit_hash,
            )

            updated_workflow = await workflow_service.update_workflow(
                workflow_id=workflow_id,
                tenant_info=tenant_info,
                display_name=workflow_name,
                git_repository=git_repository,
                generated_files=generated_files,
                status=WorkflowStatus.COMPLETED,
                bb_context_id=context.runtime.browser_session.context_id
                if context.runtime.browser_session
                else "",
            )
            if not updated_workflow:
                await cl.Message(
                    content=f"⚠️ Failed to update workflow **{workflow_name}**. Please try again."
                ).send()
                return

            # Create session workflow object
            workflow = Workflow(
                name=workflow_name,
                mongo_id=updated_workflow.id,
                thread_id=cl.context.session.thread_id,
                is_executable=True,
                commit_hash=commit_hash,
                bb_context_id=context.runtime.browser_session.context_id
                if context.runtime.browser_session
                else "",
            )

            # Set the workflow in the chainlit state manager
            await self.state_manager.set_workflow(workflow)
            await self.state_manager.construct_and_send_window_message(
                type=WindowMessageType.WORKFLOW_READY,
            )
            await cl.Message(
                content=f"💾 **Workflow Saved!** '{workflow_name}' is now available for execution.\n\n💡"
            ).send()

            # TODO: Also update the thread name to the workflow name

    async def _check_for_workflow_completion(
        self, context: MessageContext
    ) -> bytes | None:
        """Check if the workflow is complete"""
        if not context.runtime:
            return None
        # Check if the MANIFEST.JSON file exists in the runtime_workspace/session_id/ directory
        try:
            file_bytes = await context.runtime.read_bytes(
                GENERATED_MANIFEST_FILE_NAME
            )
            if file_bytes:
                info("Workflow manifest found, hence saving the workflow")
                return file_bytes
            else:
                return None
        except Exception:
            return None

    async def _check_for_sop_completion(
        self, context: MessageContext
    ) -> bytes | None:
        """Check if the SOP is complete"""
        if not context.runtime:
            return None
        # Check if the SOP.md file exists in the runtime_workspace/session_id/ directory
        try:
            return await context.runtime.read_bytes("SOP.md")
        except Exception:
            return None
