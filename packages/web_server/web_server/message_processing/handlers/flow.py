"""
Build mode handler using turn-by-turn agent (Orbot) for intelligent automation building.
"""

import json
from typing import cast

import chainlit as cl
from orbot.turn_by_turn_agent import TurnByTurnAgent, TurnResult, TurnType

from common.log import debug, error, info, warn
from common.sandbox.base import RuntimeError
from web_server.message_processing.handlers.file_upload_handler import (
    LOCAL_STORAGE_PATH,
)
from web_server.session.runtime_manager import get_runtime_session_manager
from web_server.session.state_manager import AppMode, StateKey

from ..base import MessageContext, MessageHandler, ProcessingResult


class Flow(MessageHandler):
    """Handler for build mode using turn-by-turn conversation approach with Orbot."""

    async def can_handle(self, context: MessageContext) -> bool:
        return self.state_manager.get_app_mode() == AppMode.BUILD

    async def handle(self, context: MessageContext) -> ProcessingResult:
        """Handle build mode conversation using flow agent."""
        try:
            info(
                f"Processing build mode message with Orbot: {context.content[:100]}{'...' if len(context.content) > 100 else ''}"
            )

            # Get or create Orbot agent for this session
            agent = cast(
                TurnByTurnAgent,
                self.state_manager.get_state_value(StateKey.FLOW),
            )

            # Append uploaded elements information to the content
            enriched_content = await self._enrich_content_with_uploads(context)

            # Process message through turn-by-turn agent
            async for turn_result in agent.process_user_message(
                enriched_content
            ):
                # render SOP
                runtime = await get_runtime_session_manager().get_runtime(
                    session_id=context.session_id
                )
                try:
                    sop = await runtime.read_file("SOP.md")
                    await cl.Message(content=sop, tags=["sop"]).send()
                except RuntimeError:
                    pass
                await self._handle_turn_result(turn_result)

            return ProcessingResult.handled()

        except Exception as e:
            error(f"Error in build mode handler: {e}")
            await cl.Message(content=f"❌ Error: {str(e)}").send()
            return ProcessingResult.error(e)

    async def _handle_turn_result(self, turn_result: TurnResult) -> None:
        """Handle individual turn results as Chainlit steps."""

        debug(f"Processing turn result - type: {turn_result.turn_type.value}, tool: {turn_result.tool_name}")
        
        if turn_result.turn_type in (TurnType.AI_THINKING, TurnType.AI_RESPONSE):
            # Create thinking step
            async with cl.Step(name="thinking", type="llm") as thinking_step:
                thinking_step.output = turn_result.content
        elif turn_result.turn_type == TurnType.TOOL_CALL:
            if turn_result.tool_name == "message_notify_user":
                # message already been sent in the tool
                return
            elif turn_result.tool_name == "message_ask_user":
                await self._handle_ask_user_tool(turn_result)
            else:
                # Handle file-related tools with special element creation
                await self._handle_tools_turn_result(turn_result)

        elif turn_result.turn_type == TurnType.SPEAKER_SELECTION:
            return

        elif turn_result.turn_type == TurnType.ERROR:
            raise Exception(f"{str(turn_result.content)}")

    async def _handle_ask_user_tool(self, turn_result: TurnResult) -> None:
        """Handle message_ask_user tool with file elements."""
        try:
            # Parse tool call input to extract attachments
            tool_input = turn_result.metadata.get("tool_call_input", {})
            if isinstance(tool_input, str):
                try:
                    tool_input = json.loads(tool_input)
                except json.JSONDecodeError:
                    tool_input = {}
            
            # Extract the question text and attachments
            question_text = tool_input.get("text", turn_result.content)
            attachments = tool_input.get("attachments", [])
            
            # Ensure attachments is a list
            if isinstance(attachments, str):
                attachments = [attachments]
            elif not isinstance(attachments, list):
                attachments = []
            
            # Create file elements for attachments
            elements = []
            if attachments:
                runtime = await get_runtime_session_manager().get_runtime(
                    session_id=self.state_manager.get_session_id()
                )
                
                for attachment in attachments:
                    try:
                        element = await self._create_attachment_element(attachment, runtime)
                        if element:
                            elements.append(element)
                    except Exception as e:
                        debug(f"Failed to create element for {attachment}: {e}")
            
            # The actual AskUserMessage is handled by the tool itself,
            # but we create a step to show the tool execution
            await cl.Message(
                content=question_text,
                elements=elements if elements else None
            ).send()
                    
        except Exception as e:
            error(f"Error handling ask_user tool: {e}")
            # Fall back to standard tool handling
            async with cl.Step(name="message_ask_user", type="tool") as tool_step:
                tool_step.output = turn_result.content

    async def _handle_tools_turn_result(self, turn_result: TurnResult) -> None:
        """Handle tool calls that may involve files and create appropriate elements."""

        if turn_result.tool_name == "update_todos":
            # Send progress updates to user.
            msg = await cl.Message(content=turn_result.content).send()
            # Render task list from todos.json
            await self._render_todo_tasklist(msg)
        else:
            async with cl.Step(name=turn_result.tool_name or "tool", type="tool") as tool_step:
                tool_step.input = turn_result.metadata.get("tool_call_input", {})
                tool_step.output = f"{turn_result.content[:1000]}{'...' if len(turn_result.content) > 1000 else ''}"
                # Check if this is a file-related tool and create elements if needed
                file_tools = ["edit", "write_file", "read_file"]
                if turn_result.tool_name in file_tools:
                    try:
                        # Parse tool input to get file path
                        tool_input = turn_result.metadata.get("tool_call_input", {})
                        if isinstance(tool_input, str):
                            try:
                                tool_input = json.loads(tool_input)
                            except json.JSONDecodeError:
                                tool_input = {}
                        
                        # Extract file path based on tool type
                        file_path = tool_input.get("relative_path")
                        
                        # Create file element if we have a valid file path and the operation was successful
                        if file_path and "success" in turn_result.metadata.get("status", ""):
                            runtime = await get_runtime_session_manager().get_runtime(
                                session_id=self.state_manager.get_session_id()
                            )
                            
                            element = await self._create_attachment_element(file_path, runtime)
                            if element:
                                tool_step.elements = [element]
                    except Exception as e:
                        warn(f"Failed to create file element for {turn_result}: {e}")

    async def _create_attachment_element(self, attachment: str, runtime):
        """
        Create a Chainlit element based on the attachment type.
        
        Args:
            attachment: The attachment path or URL
            runtime: The runtime instance for reading files
            
        Returns:
            A Chainlit element (Text for URLs, File for files) or None if creation fails
        """
        try:
            runtime = await get_runtime_session_manager().get_runtime(
                    session_id=self.state_manager.get_session_id()
                )
            if attachment.startswith(('http://', 'https://')):
                # Create a text element for URLs
                return cl.Text(
                    name=attachment.split('/')[-1] or "Link",
                    content=attachment,
                    display="inline"
                )
            else:
                # Read file content using runtime
                try:
                    file_content = await runtime.read_bytes(attachment)
                except Exception:
                    # Try reading as text and encode
                    file_text = await runtime.read_file(attachment)
                    file_content = file_text.encode('utf-8')
                
                # Create file element
                return cl.File(
                    name=attachment.split('/')[-1],
                    content=file_content,
                    display="inline"
                )
        except Exception as e:
            debug(f"Error creating element for {attachment}: {e}")
            return None

    async def _render_todo_tasklist(self, msg: cl.Message) -> None:
        """Render the todo list from todos.json as a Chainlit TaskList."""
        try:
            runtime = await get_runtime_session_manager().get_runtime(
                session_id=self.state_manager.get_session_id()
            )

            # Read the todos.json file
            try:
                todos_json = await runtime.read_file("todos.json")
                todos_data = json.loads(todos_json)
            except Exception as e:
                error(f"No todos.json file found or error reading: {e}")
                return

            # Create Chainlit TaskList
            task_list = cl.TaskList()

            # Add tasks from todos.json
            for todo_item in todos_data:
                # Map our status to Chainlit TaskStatus
                status_mapping = {
                    "Not Started": cl.TaskStatus.READY,
                    "In Progress": cl.TaskStatus.RUNNING,
                    "Completed": cl.TaskStatus.DONE,
                }

                task = cl.Task(
                    title=todo_item["name"],
                    status=status_mapping.get(
                        todo_item["status"], cl.TaskStatus.READY
                    ),
                ) 
                if task.status == cl.TaskStatus.RUNNING:
                  task.forId = msg.id
                await task_list.add_task(task)

            # Calculate completion percentage
            total_tasks = len(todos_data)
            completed_tasks = sum(
                1 for todo in todos_data if todo["status"] == "Completed"
            )

            if total_tasks > 0:
                completion_percentage = int(
                    (completed_tasks / total_tasks) * 100
                )
                if completion_percentage < 100:
                    task_list.status = f"Progress: ({completion_percentage}%)"
                else:
                    task_list.status = "Complete"
                await task_list.send()

        except Exception as e:
            error(f"Error rendering todo tasklist: {e}")

    async def _enrich_content_with_uploads(self, context: MessageContext) -> str:
        """Append information about uploaded files to the message content.
        
        Also saves uploaded files to the runtime workspace so they can be accessed by the agent.
        
        Args:
            context: The message context containing the original message and elements
            
        Returns:
            The enriched content with file upload information appended
        """
        enriched_content = context.content
        
        # Check if there are any uploaded elements
        if context.elements:
            file_info_lines = ["\n\n[Uploaded Files:]"]
            
            for element in context.elements:
                if hasattr(element, 'name') and hasattr(element, 'content'):
                    # Handle file elements
                    file_name = getattr(element, 'name', 'Unknown')
                    
                    # Build file info string
                    file_info = f"- {file_name}"
                    file_info += f" - Saved to: {LOCAL_STORAGE_PATH + file_name}"
                    file_info_lines.append(file_info)
            
            # Append file information to the content
            enriched_content += "\n".join(file_info_lines)
            info(f"Enriched message with {len(context.elements)} uploaded element(s)")
        
        return enriched_content


# Export the handler
__all__ = ["Flow"]
