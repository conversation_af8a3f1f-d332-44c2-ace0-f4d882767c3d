from datetime import datetime
from enum import Enum
from typing import ClassVar

from bson import ObjectId
from pydantic import BaseModel, ConfigDict, Field

from common.models.base import OrbyDataModel
from common.utils.constants import WORKFLOW_COLLECTION


class WorkflowStatus(str, Enum):
    """Enum for workflow status."""

    DRAFT = "draft"
    COMPLETED = "completed"


class WorkflowGitRepository(BaseModel):
    """Git repository information"""

    repo_url: str
    commit_hash: str
    branch: str | None = None


class WorkflowGeneratedFiles(BaseModel):
    """Generated files referenced by user file IDs"""

    model_config = ConfigDict(
        arbitrary_types_allowed=True,
    )

    sop_file_id: ObjectId
    manifest_file_id: ObjectId


class Workflow(OrbyDataModel):
    """Workflow model matching proto definition for alpha implementation"""

    IS_SINGLE_TENANT_COLLECTION: ClassVar[bool] = True

    model_config = ConfigDict(
        arbitrary_types_allowed=True,
    )

    id: ObjectId = Field(default_factory=ObjectId, alias="_id")
    thread_id: str | None = None
    display_name: str | None = None
    org_id: ObjectId | None = None
    creator_id: ObjectId | None = None
    created_at: datetime | None = None
    updated_at: datetime | None = None
    git_repository: WorkflowGitRepository | None = None
    generated_files: WorkflowGeneratedFiles | None = None
    status: WorkflowStatus = WorkflowStatus.DRAFT
    bb_context_id: str | None = None

    @classmethod
    def get_collection_name(cls) -> str:
        return WORKFLOW_COLLECTION
