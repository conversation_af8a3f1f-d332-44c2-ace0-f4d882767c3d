import asyncio
import os
from pathlib import Path
import shutil
from typing import IO

from common.constants.e2b_constants import REPO_NAME
from common.log import error as log_error
from common.log import info as log_info
from common.log import warn as log_warn
from common.sandbox.base import (
    BrowserSession,
    ExecutionResult,
    Runtime,
    RuntimeConfig,
    RuntimeExecutionError,
    RuntimeInitializationError,
    RuntimeType,
)


class LocalRuntime(Runtime):
    """
    Local runtime implementation that creates isolated workspace directories.

    Creates workspace at: {cwd}/runtime_workspace/{chainlit_session_id}/
    This provides isolation between different chains.
    """

    def __init__(self, config: RuntimeConfig):
        super().__init__(config)
        self._workspace_path: Path | None = None
        self._initialized = False
        self.runtime_id = f"local_{config.chainlit_session_id}"
        # Persistent bash session management
        self._bash_process: asyncio.subprocess.Process | None = None
        self._bash_session_active = False

    @property
    def runtime_type(self) -> RuntimeType:
        return RuntimeType.LOCAL

    @property
    def is_previous_runtime_session_restored(self) -> bool:
        # Local runtime session is never restored from previous runtime session.
        return False

    @property
    def browser_session(self) -> BrowserSession | None:
        """Get the browser session."""
        if self.config.use_browser_session:
            # Currently, local runtime does not have a remote browser session.
            return None
        return None

    @property
    def sandbox_session_id(self) -> str | None:
        """Get the id of the sandbox session."""
        return None

    @property
    def browser_connection_url(self) -> str | None:
        """Get the browser connection URL for CDP."""
        return None

    @property
    def runtime_path(self) -> Path:
        """Returns the runtime workspace directory path as string."""
        if not self._initialized or self._workspace_path is None:
            raise RuntimeInitializationError("Runtime is not initialized")
        return self._workspace_path

    def get_full_path(self, relative_path: str) -> Path:
        """
        Get the full path based on relative path to the working directory.
        Working directory is in the format of .../runtime_workspace/{session_id}

        Args:
            relative_path: Path relative to the runtime workspace root.
                          Supports both relative and absolute paths with security validation.

        Returns:
            Full path as Path object
        """
        if not self._initialized or self._workspace_path is None:
            raise RuntimeInitializationError("Runtime is not initialized")

        # Use the existing resolve_safe_path method for security validation
        safe_relative_path = self.resolve_safe_path(relative_path)
        full_path = self._workspace_path / safe_relative_path
        return full_path

    async def initialize_internal(self) -> None:
        """Initialize the local runtime workspace and environment."""
        try:
            # Create workspace: {cwd}/runtime_workspace/{session_id}
            current_dir = Path.cwd()
            self._workspace_path = (
                current_dir
                / "runtime_workspace"
                / self.config.chainlit_session_id
            )
            self._workspace_path.mkdir(parents=True, exist_ok=True)

            # Set environment variables
            if self.config.envs:
                os.environ.update(self.config.envs)

            self._initialized = True
            log_info(
                f"[LocalRuntime]: Initialized {self.runtime_id} at {self._workspace_path}"
            )

        except Exception as e:
            raise RuntimeInitializationError(
                f"Error initializing local runtime: {e}"
            ) from e

    async def read_file(self, path: str) -> str:
        """Read a file from the runtime workspace."""
        if not self._initialized:
            raise RuntimeInitializationError("Runtime is not initialized")
        try:
            # Use the get_full_path method
            full_path = self.get_full_path(path)
            with open(str(full_path), encoding="utf-8") as f:
                return f.read()
        except FileNotFoundError as e:
            raise RuntimeExecutionError(f"File not found: {path}") from e
        except PermissionError as e:
            raise RuntimeExecutionError(
                f"Permission denied reading file: {path}"
            ) from e
        except Exception as e:
            raise RuntimeExecutionError(
                f"Error reading file {path}: {e}"
            ) from e

    async def read_bytes(self, path: str) -> bytes:
        """Read image data from the runtime workspace."""
        if not self._initialized:
            raise RuntimeInitializationError("Runtime is not initialized")
        try:
            # Use the get_full_path method
            full_path = self.get_full_path(path)
            with open(str(full_path), "rb") as f:
                return f.read()
        except FileNotFoundError as e:
            raise RuntimeExecutionError(f"Image file not found: {path}") from e
        except PermissionError as e:
            raise RuntimeExecutionError(
                f"Permission denied reading image file: {path}"
            ) from e
        except Exception as e:
            raise RuntimeExecutionError(
                f"Error reading image file {path}: {e}"
            ) from e

    async def write_file(self, path: str, content: str | bytes | IO) -> None:
        """Write content to a file in the runtime workspace."""
        if not self._initialized:
            raise RuntimeInitializationError("Runtime is not initialized")
        try:
            # Use the get_full_path method
            full_path = self.get_full_path(path)
            full_path.parent.mkdir(parents=True, exist_ok=True)

            if isinstance(content, str):
                with open(full_path, "w", encoding="utf-8") as f:
                    f.write(content)
            elif isinstance(content, bytes):
                with open(full_path, "wb") as f:
                    f.write(content)
            elif isinstance(content, IO):  # IO object
                content_data = content.read()
                if isinstance(content_data, bytes):
                    with open(full_path, "wb") as f:
                        f.write(content_data)
                else:
                    with open(full_path, "w", encoding="utf-8") as f:
                        f.write(content_data)
            else:
                raise RuntimeExecutionError(
                    f"Unsupported content type: {type(content)}"
                )

            log_info(f"[LocalRuntime]: Wrote file {path}")
        except PermissionError as e:
            raise RuntimeExecutionError(
                f"Permission denied writing file: {path}"
            ) from e
        except Exception as e:
            raise RuntimeExecutionError(
                f"Error writing file {path}: {e}"
            ) from e

    async def pull_code(self, commit_hash: str, repo_url: str) -> str:
        """Pull the code from the remote repository."""
        return ""

    async def push_workspace(self) -> str:
        """Push a directory to the runtime environment."""
        if not self._initialized or self._workspace_path is None:
            raise RuntimeInitializationError("Runtime is not initialized")
        try:
            random_branch_name = (
                f"local_runtime_{self.config.chainlit_session_id}"
            )
            git_file = os.path.join(
                os.getcwd(),
                "packages",
                "web_server",
                "web_server",
                "utils",
                "e2b",
                "git-push.sh",
            )
            result = await self.run_command(
                git_file,
                # Use --no-sudo for local development
                [
                    REPO_NAME,
                    str(self._workspace_path),
                    random_branch_name,
                    "--no-sudo",
                ],
            )
            return result.stdout
        except Exception as e:
            raise RuntimeExecutionError(
                f"Error pushing directory {self._workspace_path}: {e}"
            ) from e

    async def run_command(
        self,
        command: str,
        args: list[str],
        work_dir: str | None = None,
        envs: dict[str, str] | None = None,
    ) -> ExecutionResult:
        """Execute a command in the runtime environment."""
        if not self._initialized:
            raise RuntimeInitializationError("Runtime is not initialized")
        try:
            full_command = [command] + args
            # Resolve working directory using the get_full_path method
            if work_dir is None:
                working_directory = str(self._workspace_path)
            else:
                working_directory = str(self.get_full_path(work_dir))

            result = await self._run_subprocess(
                full_command, cwd=working_directory, envs=envs
            )
            log_info(
                f"[LocalRuntime]: Executed '{command}' with return code {result.returncode}"
            )
            return result
        except PermissionError as e:
            raise RuntimeExecutionError(
                f"Permission denied running command {command}: {e}"
            ) from e
        except Exception as e:
            raise RuntimeExecutionError(
                f"Error running command {command}: {e}"
            ) from e

    async def run_bash_command(
        self,
        command: str,
        work_dir: str | None = None,
        envs: dict[str, str] | None = None,
    ) -> ExecutionResult:
        """Execute a bash command in the runtime environment."""
        return await self.run_command(
            command="bash",
            args=["-c", command],
        )

    async def run_code(
        self,
        work_dir: str | None = None,
        entry_point: str = "main.py",
        args: list[str] | None = None,
        envs: dict[str, str] | None = None,
    ) -> ExecutionResult:
        """
        Run Python code (entry_point) in the runtime environment.
        Creates venv, installs requirements, then runs entry_point.
        """
        if not self._initialized or self._workspace_path is None:
            raise RuntimeInitializationError("Runtime is not initialized")

        try:
            # Resolve working directory using the get_full_path method
            if work_dir is None:
                working_directory_path = self._workspace_path
                relative_work_dir = None
            else:
                working_directory_path = self.get_full_path(work_dir)
                relative_work_dir = work_dir

            # Check entry_point exists
            entry_point_path = working_directory_path / entry_point
            if not entry_point_path.exists():
                raise RuntimeExecutionError(
                    f"{entry_point} not found in {working_directory_path}"
                )

            # Create venv if needed
            venv_path = working_directory_path / "venv"
            if not venv_path.exists():
                log_info(f"Creating virtual environment at {venv_path}")
                result = await self.run_command(
                    "python3", ["-m", "venv", "venv"], relative_work_dir
                )
                if result.returncode != 0:
                    raise RuntimeExecutionError(
                        f"Failed to create venv: {result.stderr}"
                    )

            # Install requirements
            requirements_path = working_directory_path / "requirements.txt"
            if requirements_path.exists():
                log_info("Installing requirements in new virtual environment")
                result = await self.run_command(
                    "venv/bin/pip",
                    ["install", "-r", "requirements.txt"],
                    relative_work_dir,
                )
                if result.returncode != 0:
                    log_warn(f"Failed to install requirements: {result.stderr}")

            # Run entry_point with environment variables
            result = await self.run_command(
                "venv/bin/python",
                [entry_point] + (args or []),
                relative_work_dir,
                envs,
            )
            log_info(
                f"[LocalRuntime]: Executed {entry_point} with return code {result.returncode}"
            )
            return result
        except Exception as e:
            raise RuntimeExecutionError(f"Error running code: {e}") from e

    async def stop(self) -> None:
        """Stop the runtime and clean up workspace."""
        if not self._initialized:
            raise RuntimeInitializationError("Runtime is not initialized")
        try:
            if self._workspace_path and self._workspace_path.exists():
                shutil.rmtree(self._workspace_path)
            self._workspace_path = None
            self._initialized = False
            log_info(f"[LocalRuntime]: Stopped {self.runtime_id}")
        except Exception as e:
            raise RuntimeExecutionError(
                f"Error stopping local runtime: {e}"
            ) from e

    def resolve_safe_path(self, path: str) -> Path:
        """
        Resolve path safely within workspace boundaries.
        Prevents path traversal attacks and ensures workspace isolation.
        Returns the path relative to workspace root.
        """
        if not self._initialized or self._workspace_path is None:
            raise RuntimeInitializationError("Runtime is not initialized")

        path_obj = Path(path)

        # If the provided path is absolute, convert it to a relative path by stripping its anchor.
        if path_obj.is_absolute():
            try:
                # Try to get the relative path from the workspace root
                relative_from_workspace = path_obj.relative_to(
                    self._workspace_path.resolve()
                )
                # If successful, this path is already within workspace - return the relative part
                path_obj = relative_from_workspace
            except ValueError:
                # Path is not within workspace, so treat it as a path to be resolved relative to workspace
                try:
                    path_obj = path_obj.relative_to(path_obj.anchor)
                except ValueError:
                    # Fallback: manually remove the first part (e.g. the drive or root) if relative_to fails
                    path_obj = (
                        Path(*path_obj.parts[1:])
                        if len(path_obj.parts) > 1
                        else Path(".")
                    )

        # Security check: Explicitly reject paths containing parent directory references
        # This prevents path traversal attacks and maintains expected "Permission denied" behavior
        if ".." in path_obj.parts:
            raise PermissionError(
                f"Path '{path}' resolves outside workspace boundaries"
            )

        # Normalize the path to handle '.' components and empty parts
        normalized_parts = []
        for part in path_obj.parts:
            if (
                part != "." and part
            ):  # Skip current directory references and empty parts
                normalized_parts.append(part)

        # Reconstruct the safe path
        if not normalized_parts:
            safe_path = Path(".")
        else:
            safe_path = Path(*normalized_parts)

        # Resolve the path relative to the workspace directory to get absolute path
        resolved_absolute_path = (self._workspace_path / safe_path).resolve()

        # Final security check: ensure that the resolved path is within the workspace boundaries
        try:
            relative_path = resolved_absolute_path.relative_to(
                self._workspace_path.resolve()
            )
        except ValueError as exc:
            raise PermissionError(
                f"Path '{path}' resolves outside workspace boundaries"
            ) from exc

        return relative_path

    async def _run_subprocess(
        self,
        command: list[str],
        cwd: str | None = None,
        envs: dict[str, str] | None = None,
    ) -> ExecutionResult:
        """Execute subprocess command asynchronously with real-time output streaming."""
        if not self._initialized or self._workspace_path is None:
            raise RuntimeInitializationError("Runtime is not initialized")

        try:
            if envs:
                env = os.environ.copy()
                env.update(envs)
            else:
                env = None

            process = await asyncio.create_subprocess_exec(
                *command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=cwd or str(self._workspace_path),
                env=env,
            )

            # Collect output while streaming
            stdout_output = []
            stderr_output = []

            async def read_stdout():
                """Read and stream stdout."""
                if process.stdout is None:
                    return
                while True:
                    line = await process.stdout.readline()
                    if not line:
                        break
                    # format line to remove new spaces
                    line_str = line.decode("utf-8", errors="replace").rstrip(
                        "\n\r"
                    )
                    if line_str:
                        stdout_output.append(line_str)
                        self._stream_stdout(line_str)

            async def read_stderr():
                """Read and stream stderr."""
                if process.stderr is None:
                    return
                while True:
                    line = await process.stderr.readline()
                    if not line:
                        break
                    line_str = line.decode("utf-8", errors="replace").rstrip(
                        "\n\r"
                    )
                    if line_str:
                        stderr_output.append(line_str)
                        self._stream_stderr(line_str)

            # Run stdout and stderr readers concurrently
            await asyncio.gather(read_stdout(), read_stderr())

            # Wait for process to complete
            await process.wait()

            return ExecutionResult(
                returncode=process.returncode or 0,
                stdout="\n".join(stdout_output),
                stderr="\n".join(stderr_output),
            )
        except Exception as e:
            raise RuntimeExecutionError(f"Error running subprocess: {e}") from e

    def _stream_stdout(self, output: str) -> None:
        """Stream command stdout output and propagate as runtime event."""
        # Add to log streamer (filtering handled by streamer)
        log_info(f"[LocalRuntime]: [stdout] {output}")
        self.log_streamer.add_log("stdout", output)

    def _stream_stderr(self, output: str) -> None:
        """Stream command stderr output and propagate as runtime event."""
        # Add to log streamer
        log_error(f"[LocalRuntime]: [stderr] {output}")
        self.log_streamer.add_log("stderr", output)
    
    def start_file_watcher(self) -> None:
        """Start the file watcher for the runtime workspace."""
        pass

    # Browser interaction methods - not supported in local runtime
    async def browser_click(
        self,
        index: int | None = None,
        coordinate_x: float | None = None,
        coordinate_y: float | None = None,
    ) -> str:
        """Click on elements in the current browser page."""
        raise RuntimeExecutionError(
            "Browser operations are not supported in LocalRuntime. Use E2BRuntime with use_browser_session=True for browser automation."
        )

    async def browser_input(
        self,
        text: str,
        press_enter: bool,
        index: int | None = None,
        coordinate_x: float | None = None,
        coordinate_y: float | None = None,
    ) -> str:
        """Overwrite text in editable elements on the current browser page."""
        raise RuntimeExecutionError(
            "Browser operations are not supported in LocalRuntime. Use E2BRuntime with use_browser_session=True for browser automation."
        )

    async def browser_move_mouse(
        self, coordinate_x: float, coordinate_y: float
    ) -> str:
        """Move cursor to specified position on the current browser page."""
        raise RuntimeExecutionError(
            "Browser operations are not supported in LocalRuntime. Use E2BRuntime with use_browser_session=True for browser automation."
        )

    async def browser_press_key(self, key: str) -> str:
        """Simulate key press in the current browser page."""
        raise RuntimeExecutionError(
            "Browser operations are not supported in LocalRuntime. Use E2BRuntime with use_browser_session=True for browser automation."
        )

    async def browser_select_option(self, index: int, option: int) -> str:
        """Select specified option from dropdown list element."""
        raise RuntimeExecutionError(
            "Browser operations are not supported in LocalRuntime. Use E2BRuntime with use_browser_session=True for browser automation."
        )

    async def browser_scroll_up(self, to_top: bool | None = None) -> str:
        """Scroll up the current browser page."""
        raise RuntimeExecutionError(
            "Browser operations are not supported in LocalRuntime. Use E2BRuntime with use_browser_session=True for browser automation."
        )

    async def browser_scroll_down(self, to_bottom: bool | None = None) -> str:
        """Scroll down the current browser page."""
        raise RuntimeExecutionError(
            "Browser operations are not supported in LocalRuntime. Use E2BRuntime with use_browser_session=True for browser automation."
        )

    async def browser_console_exec(self, javascript: str) -> str:
        """Execute JavaScript code in browser console."""
        raise RuntimeExecutionError(
            "Browser operations are not supported in LocalRuntime. Use E2BRuntime with use_browser_session=True for browser automation."
        )

    async def browser_console_view(self, max_lines: int | None = None) -> str:
        """View browser console output."""
        raise RuntimeExecutionError(
            "Browser operations are not supported in LocalRuntime. Use E2BRuntime with use_browser_session=True for browser automation."
        )

    async def browser_view(self) -> str:
        """View content of the current browser page."""
        raise RuntimeExecutionError(
            "Browser operations are not supported in LocalRuntime. Use E2BRuntime with use_browser_session=True for browser automation."
        )

    async def browser_navigate(self, url: str) -> str:
        """Navigate browser to specified URL."""
        raise RuntimeExecutionError(
            "Browser operations are not supported in LocalRuntime. Use E2BRuntime with use_browser_session=True for browser automation."
        )

    async def browser_restart(self, url: str) -> str:
        """Restart browser and navigate to specified URL."""
        raise RuntimeExecutionError(
            "Browser operations are not supported in LocalRuntime. Use E2BRuntime with use_browser_session=True for browser automation."
        )

    # ------------------------------------------------------------------
    # Browser session interface (no-ops for LocalRuntime)
    # ------------------------------------------------------------------
    async def create_browser_session(
        self, bb_context_id: str | None
    ) -> dict[str, str]:
        """Local runtime has no remote browser – nothing to create."""
        return {}

    def release_browser_session(self) -> None:  # type: ignore[override]
        """Local runtime has no remote browser – nothing to release."""
        return
