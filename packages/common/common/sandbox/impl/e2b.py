"""
E2B Cloud Runtime Implementation - Remote sandbox execution environment.
Provides isolated cloud-based runtime instances with browser session support.
"""

import asyncio
import os
from pathlib import Path
import re
import time
from typing import IO

from browserbase import Browserbase
from e2b import CommandResult, NotFoundException, Sandbox

from common.constants.e2b_constants import REPO_NAME, get_template_name
from common.log import debug, error, info, warn
from common.sandbox.base import (
    BrowserSession,
    ExecutionResult,
    NotFoundError,
    Runtime,
    RuntimeConfig,
    RuntimeExecutionError,
    RuntimeInitializationError,
    RuntimeType,
)
from common.services.proxy_configuration_service import (
    ProxyConfigurationService,
)
from web_server.core.config import server_config


class E2BRuntime(Runtime):
    """
    E2B cloud runtime implementation with browser session support.

    Creates isolated sandbox instances in the cloud with optional browser sessions.
    Workspace path: /home/<USER>/workspace/{chainlit_session_id} (fixed for E2B)
    """

    def __init__(self, config: RuntimeConfig, api_key: str | None = None):
        super().__init__(config)
        self._sandbox: Sandbox | None = None
        # Session reuse: try to restore from previous runtime session, fallback to new session
        self._previous_session_config = config.previous_runtime_session_config
        self._initialized = False
        self._workspace_path: Path | None = (
            None  # Will be set after session is determined
        )
        self._browser_session: BrowserSession | None = None
        self._session_envs = {}
        self._session_cwd = ""
        self._session_state_dir = ""
        self._session_env_file = ""
        self._session_pwd_file = ""
        self._is_previous_session_restored = False
        self._workflow_commit_hash = config.workflow_commit_hash
        # Validate required API keys
        self._validate_api_keys(api_key)

        # Initialize browser client if needed
        bb_api_key = os.getenv("BB_API_KEY")
        if not bb_api_key:
            raise RuntimeInitializationError(
                "Browserbase API key is not set (BB_API_KEY environment variable)"
            )
        self._browser_base = Browserbase(api_key=bb_api_key)

        # Initialize proxy configuration service
        self._proxy_configuration_service = ProxyConfigurationService()

    def _validate_api_keys(self, api_key: str | None = None):
        """Validate required API keys for E2B and Browserbase."""
        if not os.getenv("E2B_API_KEY") and not api_key:
            raise RuntimeInitializationError(
                "E2B API key is not set (E2B_API_KEY environment variable)"
            )

        if self.config.use_browser_session:
            if not os.getenv("BB_API_KEY"):
                raise RuntimeInitializationError(
                    "Browserbase API key is not set (BB_API_KEY environment variable)"
                )
            if not os.getenv("BB_PROJECT_ID"):
                raise RuntimeInitializationError(
                    "Browserbase project ID is not set (BB_PROJECT_ID environment variable)"
                )

    @property
    def runtime_type(self) -> RuntimeType:
        return RuntimeType.E2B

    @property
    def is_previous_runtime_session_restored(self) -> bool:
        """Get the flag indicating if the previous runtime session was restored."""
        return self._is_previous_session_restored

    @property
    def sandbox_session_id(self) -> str | None:
        """Get the id of the sandbox session."""
        if self._sandbox:
            return self._sandbox.sandbox_id
        return None

    @property
    def browser_session(self) -> BrowserSession | None:
        """Get the browser session."""
        if self._browser_session:
            return self._browser_session
        return None

    @property
    def runtime_path(self) -> Path:
        """Returns the E2B workspace path."""
        if not self._initialized or not self._workspace_path:
            raise RuntimeInitializationError("Sandbox is not initialized")
        return self._workspace_path

    def get_full_path(self, relative_path: str) -> Path:
        """
        Get the full path based on relative path to the working directory.
        Working directory is in the format of .../runtime_workspace/{chainlit_session_id}

        Args:
            relative_path: Path relative to the runtime workspace root.
                          E2B only supports relative paths.

        Returns:
            Full path as Path object
        """
        if not self._initialized or not self._workspace_path:
            raise RuntimeInitializationError("Sandbox is not initialized")

        # E2B paths are relative to workspace - strip leading slash if present
        clean_path = relative_path.lstrip("/")
        full_path = self._workspace_path / clean_path
        return full_path

    async def initialize_internal(self) -> None:
        """Initialize the E2B sandbox environment with optional browser session."""
        if self._initialized:
            raise RuntimeInitializationError("Sandbox is already initialized")

        start_time = time.time()
        info(
            f"[E2BRuntime]: Starting initialization for session {self.config.chainlit_session_id}"
        )

        try:
            envs = self.config.envs.copy()

            # Set up browser session if requested
            browser_setup_time = 0
            if self.config.use_browser_session:
                browser_start_time = time.time()
                session_envs = await self.create_browser_session(
                    self.config.bb_context_id
                )
                envs.update(session_envs)
                browser_setup_time = time.time() - browser_start_time
                info(
                    f"[E2BRuntime]: Browser session setup completed in {browser_setup_time:.2f}s"
                )

            sandbox_start_time = time.time()

            self._sandbox = Sandbox(
                template=self.config.template
                or get_template_name(server_config.mode),
                timeout=3_600,  # 1 hour
                request_timeout=600,  # 10 minutes per request
                envs=envs,
                metadata=self._build_sandbox_metadata(),
            )

            # Now that we know which session we're using, set the correct workspace path
            if (
                self._is_previous_session_restored
                and self._previous_session_config
            ):
                session_id = self._previous_session_config.chainlit_session_id
            else:
                session_id = self.config.chainlit_session_id

            self._workspace_path = Path(f"/home/<USER>/workspace/{session_id}")

            sandbox_setup_time = time.time() - sandbox_start_time
            info(
                f"[E2BRuntime]: Sandbox creation completed in {sandbox_setup_time:.2f}s"
            )

            # Store runtime ID for future reference
            self.runtime_id = self._sandbox.sandbox_id
            self._initialized = True

            # Setup workspace directory in the sandbox
            workspace_start_time = time.time()
            self._setup_workspace()
            workspace_setup_time = time.time() - workspace_start_time

            info(
                f"[E2BRuntime]: Workspace setup completed in {workspace_setup_time:.2f}s"
            )

            total_time = time.time() - start_time
            browser_session_info = ""
            if self.config.use_browser_session and self._browser_session:
                browser_session_info = (
                    f" with browser session {self._browser_session.id}"
                )

            info(
                f"[E2BRuntime]: Successfully initialized sandbox {self._sandbox.sandbox_id} "
                f"for session {self.config.chainlit_session_id}{browser_session_info} "
                f"(Total time: {total_time:.2f}s, Browser: {browser_setup_time:.2f}s, "
                f"Sandbox: {sandbox_setup_time:.2f}s, Workspace: {workspace_setup_time:.2f}s)"
            )

            # Run final setup tasks in parallel
            setup_tasks = []

            # Add code pulling task if needed
            if (
                self._workflow_commit_hash
                and not self._is_previous_session_restored
            ):
                info(
                    f"[E2BRuntime]: Starting code pull from repository {REPO_NAME} (commit: {self._workflow_commit_hash})"
                )
                setup_tasks.append(
                    self.pull_code(self._workflow_commit_hash, REPO_NAME)
                )

            # Add vibe-automation installation task
            info(
                "[E2BRuntime]: Starting installation of vibe-automation package"
            )
            setup_tasks.append(self._install_vibe_automation_background())

            # Run setup tasks in parallel if any exist
            if setup_tasks:
                await asyncio.gather(*setup_tasks, return_exceptions=True)

        except Exception as e:
            total_time = time.time() - start_time
            error(
                f"[E2BRuntime]: Initialization failed after {total_time:.2f}s: {e}"
            )
            raise RuntimeInitializationError(
                f"Error initializing E2B sandbox: {e}"
            ) from e

    async def _install_vibe_automation_background(self):
        """Install vibe-automation package globally in background."""
        try:
            if not self._sandbox or not self._initialized:
                raise RuntimeInitializationError(
                    "Sandbox is not initialized or not connected"
                )
            info(
                "[E2BRuntime]: Starting background installation of vibe-automation package"
            )
            # Install globally (system-wide)
            result = self._sandbox.commands.run(
                "pip install -U vibe-automation"
            )
            if result.exit_code == 0:
                info(
                    "[E2BRuntime]: Successfully installed vibe-automation package globally"
                )
            else:
                warn(
                    f"[E2BRuntime]: Failed to install vibe-automation package: {result.stderr}"
                )
        except Exception as e:
            warn(
                f"[E2BRuntime]: Background installation of vibe-automation failed: {e}"
            )

    async def _get_proxy_configuration(self) -> list[dict] | None:
        """Get proxy configuration for browserbase."""
        try:
            # Get the configuration
            config = await self._proxy_configuration_service.get_configuration()

            # If no entries are configured, don't use proxy
            if not config.entries:
                info(
                    "[E2BRuntime]: No entries in proxy configuration, proxy disabled"
                )
                return None

            # Build proxy configurations for browserbase
            proxy_configs = []

            # Add configs for each domain pattern entry
            for entry in config.entries:
                proxy_configs.append(
                    {
                        "type": "browserbase",
                        "domainPattern": entry.domain_pattern,
                    },
                )

            info(f"[E2BRuntime]: Proxy configs: {proxy_configs}")
            return proxy_configs

        except Exception as e:
            error(f"[E2BRuntime]: Failed to get proxy configuration: {e}")
            return None

    def start_file_watcher(self, on_file_event_callback):
        """Start watching the runtime workspace for file changes.

        Args:
            on_file_event_callback: Function that receives ProcessedEvent objects
        """
        if not self._initialized or not self._sandbox:
            raise RuntimeInitializationError("Runtime is not initialized")

        if self.file_watcher.is_active():
            return  # Already watching
        info(f"[E2BRuntime]: Starting file watcher for {self._workspace_path}")
        
        try:
            # Watch the runtime workspace directory
            self.file_watcher.start(
                watch_path=str(self._workspace_path),
                on_event_callback=on_file_event_callback,
                runtime=self,
            )
            info(
                f"[E2BRuntime]: Started file watcher for {self._workspace_path}"
            )
        except Exception as e:
            error(f"[E2BRuntime]: Failed to start file watcher: {e}")
            raise RuntimeExecutionError(
                f"Failed to start file watcher: {e}"
            ) from e

    def stop_file_watcher(self):
        """Stop watching files."""
        if self.file_watcher.is_active():
            self.file_watcher.stop()
            info("[E2BRuntime]: Stopped file watcher")

    def _build_sandbox_metadata(self) -> dict:
        """Build metadata for the sandbox instance."""
        metadata = {"chainlit_session_id": self.config.chainlit_session_id}

        if self.config.use_browser_session and self._browser_session:
            metadata = {
                **metadata,
                "browser_session_id": self._browser_session.id,
                "browser_session_debugger_url": self._browser_session.debugger_url,
            }

        return metadata

    def _setup_workspace(self) -> None:
        """Create and setup the workspace directory."""
        if not self._initialized or not self._sandbox:
            raise RuntimeInitializationError("Sandbox is not initialized")
        try:
            info("[E2BRuntime]: Setting up workspace directory...")
            # Create workspace directory first (fast)
            command: CommandResult = self._sandbox.commands.run(
                f"mkdir -p {self._workspace_path} && cd {self._workspace_path}"
            )
            if command.exit_code != 0:
                raise RuntimeExecutionError(
                    f"Failed to setup workspace for runtime {self.runtime_id}: {command.stderr}"
                )
            info(
                f"[E2BRuntime]: Workspace directory created at {self._workspace_path}"
            )
        except Exception as e:
            raise RuntimeExecutionError(
                f"Failed to setup workspace for runtime {self.runtime_id}: {e}"
            ) from e

    async def read_file(self, path: str) -> str:
        """Read a file from the E2B sandbox."""
        if not self._initialized or not self._sandbox:
            raise RuntimeInitializationError("Sandbox is not initialized")
        try:
            # Use the get_full_path method
            full_path = self.get_full_path(path)
            return self._sandbox.files.read(str(full_path))
        except NotFoundException as nfe:
            raise NotFoundError(nfe) from nfe
        except Exception as e:
            raise RuntimeExecutionError(
                f"Error reading file {path}: {e}"
            ) from e

    async def read_bytes(self, path: str) -> bytearray:
        """Read image data from the E2B sandbox."""
        if not self._initialized or not self._sandbox:
            raise RuntimeInitializationError("Sandbox is not initialized")
        try:
            # Use the get_full_path method
            full_path = self.get_full_path(path)

            # E2B files.read() can handle binary files, but we need to ensure we get bytes
            # The E2B Python SDK should return bytes for binary files automatically
            data = self._sandbox.files.read(str(full_path), format="bytes")
            # E2B may return `bytearray`; cast defensively to `bytearray` for type safety.
            return bytearray(data)
        except Exception as e:
            raise RuntimeExecutionError(
                f"Error reading image file {path}: {e}"
            ) from e

    async def write_file(self, path: str, content: str | bytes | IO) -> None:
        """Write content to a file in the E2B sandbox."""
        if not self._initialized or not self._sandbox:
            raise RuntimeInitializationError("Sandbox is not initialized")
        try:
            # Use the get_full_path method
            full_path = self.get_full_path(path)
            self._sandbox.files.write(str(full_path), content)
            info(f"[E2BRuntime]: Wrote file {path}")
        except Exception as e:
            raise RuntimeExecutionError(
                f"Error writing file {path}: {e}"
            ) from e

    def _parse_claude_command(
        self, command: str, args: list[str], cwd: str
    ) -> str:
        """
        Parse claude command arguments and create an expect script for execution.

        Handles complex claude commands by creating wrapper scripts and expect automation
        to manage interactive terminal sessions properly.

        Args:
            command: The claude command
            args: List of arguments for the claude command
            cwd: Current working directory where files should be created

        Returns:
            The final shell command to execute (expect script path)
        """
        if not self._sandbox:
            raise RuntimeInitializationError("Sandbox is not initialized")

        claude_cmd_parts = [command]
        prompt_file = None
        system_prompt_file = None

        i = 0
        while i < len(args):
            arg = args[i]
            if arg == "-p" and i + 1 < len(args):
                prompt_content = args[i + 1]
                prompt_file = f"{cwd}/claude_prompt.txt"
                self._sandbox.files.write(prompt_file, prompt_content)
                claude_cmd_parts.extend(["-p", "@PROMPT_FILE@"])
                i += 2
            elif arg == "--system-prompt" and i + 1 < len(args):
                system_prompt_content = args[i + 1]
                system_prompt_file = f"{cwd}/claude_system_prompt.txt"
                self._sandbox.files.write(
                    system_prompt_file, system_prompt_content
                )
                claude_cmd_parts.extend(
                    ["--system-prompt", "@SYSTEM_PROMPT_FILE@"]
                )
                i += 2
            elif arg == "--output-format" and i + 1 < len(args):
                claude_cmd_parts.extend(["--output-format", args[i + 1]])
                i += 2
            elif arg == "--verbose":
                claude_cmd_parts.append("--verbose")
                i += 1
            elif arg == "--mcp-config" and i + 1 < len(args):
                claude_cmd_parts.extend(["--mcp-config", args[i + 1]])
                i += 2
            elif arg == "--add-dir" and i + 1 < len(args):
                dir_path = args[i + 1]
                full_dir_path = (
                    cwd if not dir_path.startswith("/") else dir_path
                )
                claude_cmd_parts.extend(["--add-dir", full_dir_path])
                i += 2
            elif arg == "--allowedTools" and i + 1 < len(args):
                claude_cmd_parts.extend(["--allowedTools", args[i + 1]])
                i += 2
            elif arg == "--continue":
                claude_cmd_parts.append("--continue")
                i += 1
            elif arg == "--dangerously-skip-permissions":
                claude_cmd_parts.append("--dangerously-skip-permissions")
                i += 1
            else:
                claude_cmd_parts.append(arg)
                i += 1

        # Build wrapper script if needed
        if prompt_file or system_prompt_file:
            wrapper_script_parts = ["#!/bin/bash"]
            if system_prompt_file:
                wrapper_script_parts.append(
                    f'SYSTEM_PROMPT_CONTENT=$(cat "{system_prompt_file}")'
                )
            if prompt_file:
                wrapper_script_parts.append(
                    f'PROMPT_CONTENT=$(cat "{prompt_file}")'
                )

            wrapper_script_parts.append("# Execute claude with file contents")
            cmd_parts = []
            for part in claude_cmd_parts:
                if part == "@SYSTEM_PROMPT_FILE@":
                    cmd_parts.append('"$SYSTEM_PROMPT_CONTENT"')
                elif part == "@PROMPT_FILE@":
                    cmd_parts.append('"$PROMPT_CONTENT"')
                else:
                    escaped_part = part.replace('"', '\\"').replace("$", "\\$")
                    cmd_parts.append(f'"{escaped_part}"')
            wrapper_script_parts.append(f"exec {' '.join(cmd_parts)}")

            wrapper_script = "\n".join(wrapper_script_parts)
            wrapper_script_path = f"{cwd}/claude_wrapper.sh"
            self._sandbox.files.write(wrapper_script_path, wrapper_script)

            expect_script = f"""#!/usr/bin/expect -f
    set timeout 600
    spawn bash "{wrapper_script_path}"
    expect {{
        "Choose the text style that looks best with your terminal:" {{
            send "1\\r"
            exp_continue
        }}
        -re "❯ .*\\\\. " {{
            send "1\\r"
            exp_continue
        }}
        ">" {{
            send "\\r"
            exp_continue
        }}
        eof {{
        }}
        timeout {{
            puts "Claude command timed out after 600 seconds"
            exit 1
        }}
    }}
    wait
    """
        else:
            escaped_parts = []
            for part in claude_cmd_parts:
                escaped_part = part.replace('"', '\\"').replace("$", "\\$")
                escaped_parts.append(f'"{escaped_part}"')
            claude_command = " ".join(escaped_parts)
            expect_script = f"""#!/usr/bin/expect -f
    set timeout 600
    spawn sh -c {claude_command}
    expect {{
        "Choose the text style that looks best with your terminal:" {{
            send "1\\r"
            exp_continue
        }}
        -re "❯ .*\\\\. " {{
            send "1\\r"
            exp_continue
        }}
        ">" {{
            send "\\r"
            exp_continue
        }}
        eof {{
        }}
        timeout {{
            puts "Claude command timed out"
            exit 1
        }}
    }}
    wait
    """

        expect_script_path = f"{cwd}/claude_expect.exp"
        self._sandbox.files.write(expect_script_path, expect_script)
        return f"expect {expect_script_path}"

    async def run_command(
        self,
        command: str,
        args: list[str],
        work_dir: str | None = None,
        envs: dict[str, str] | None = None,
    ) -> ExecutionResult:
        """Execute a command in the E2B sandbox."""
        if not self._initialized or not self._sandbox:
            raise RuntimeInitializationError("Sandbox is not initialized")
        try:
            # Determine working directory using the get_full_path method
            if work_dir is None:
                cwd = str(self._workspace_path)
            else:
                cwd = str(self.get_full_path(work_dir))

            if command.startswith("claude"):
                # Use the helper function to parse and create the expect script
                command = self._parse_claude_command(command, args, cwd)
            else:
                command = " ".join([command] + args)

            # ------------------------------------------------------------------
            # To keep the loop responsive, execute the blocking call inside a
            # background thread via `asyncio.to_thread`. All stdout/stderr
            # callbacks still fire from that worker thread and propagate events
            # through `_stream_stdout`/`_stream_stderr`
            # ------------------------------------------------------------------

            debug(
                f"[E2BRuntime]: Executing '{command}' with args {args} and envs {envs}"
            )
            result: CommandResult = await asyncio.to_thread(
                self._sandbox.commands.run,
                command,
                on_stdout=self._stream_stdout,
                on_stderr=self._stream_stderr,
                cwd=cwd,
                envs=envs,
                timeout=600,
                request_timeout=600,
            )

            info(
                f"[E2BRuntime]: Executed '{command}' with return code {result.exit_code}"
            )
            return ExecutionResult(
                returncode=result.exit_code,
                stdout=self._clean_command_output(result.stdout),
                stderr=self._clean_command_output(result.stderr),
            )
        except Exception as e:
            error(f"Error running command: {e}")
            raise RuntimeExecutionError(f"Error running command: {e}") from e

    async def run_bash_command(
        self,
        command: str,
        work_dir: str | None = None,
        envs: dict[str, str] | None = None,
    ) -> ExecutionResult:
        """Execute a bash command in the E2B sandbox."""
        if not self._initialized or not self._sandbox:
            raise RuntimeInitializationError("Sandbox is not initialized")
        try:
            # Determine working directory using the get_full_path method
            if work_dir is None:
                cwd = str(self._workspace_path)
            else:
                cwd = str(self.get_full_path(work_dir))

            # Execute the command using bash -c
            bash_command = f"bash -c {command!r}"

            debug(
                f"[E2BRuntime]: Executing bash command '{command}' with envs {envs}"
            )
            result: CommandResult = await asyncio.to_thread(
                self._sandbox.commands.run,
                bash_command,
                on_stdout=self._stream_stdout,
                on_stderr=self._stream_stderr,
                cwd=cwd,
                envs=envs,
                timeout=600,
                request_timeout=600,
            )

            info(
                f"[E2BRuntime]: Executed bash command with return code {result.exit_code}"
            )
            return ExecutionResult(
                returncode=result.exit_code,
                stdout=self._clean_command_output(result.stdout),
                stderr=self._clean_command_output(result.stderr),
            )
        except Exception as e:
            error(f"Error running bash command: {e}")
            raise RuntimeExecutionError(
                f"Error running bash command: {e}"
            ) from e

    async def run_code(
        self,
        work_dir: str | None = None,
        entry_point: str = "main.py",
        args: list[str] | None = None,
        envs: dict[str, str] | None = None,
    ) -> ExecutionResult:
        """
        Run Python code (main.py) in the E2B sandbox.
        Installs requirements if needed, then runs main.py.
        """
        if not self._initialized or not self._sandbox:
            raise RuntimeInitializationError("Sandbox is not initialized")

        try:
            # Determine working directory using the get_full_path method
            if work_dir is None:
                working_dir = None  # Use workspace root
                full_work_path = str(self._workspace_path)
            else:
                working_dir = work_dir.lstrip("/")
                full_work_path = str(self.get_full_path(work_dir))

            # Install requirements if exists
            files = self._sandbox.files.list(full_work_path)
            for f in files:
                if f.name == "requirements.txt":
                    info("Installing requirements")
                    result = await self.run_command(
                        "pip",
                        ["install", "-r", "requirements.txt"],
                        working_dir,
                        envs=envs,
                    )
                    info("Installed requirements")
                    break

            entry_point_file_found = False
            for f in files:
                if f.name == entry_point:
                    entry_point_file_found = True
                    break
            if not entry_point_file_found:
                raise RuntimeExecutionError(
                    f"{entry_point} not found in {full_work_path}"
                )

            result = await self.run_command(
                "python", [entry_point] + (args or []), working_dir, envs=envs
            )
            info(
                f"[E2BRuntime]: Executed {entry_point} with return code {result.returncode}"
            )
            return result
        except Exception as e:
            raise RuntimeExecutionError(f"Error running code: {e}") from e

    async def push_workspace(self) -> str:
        """Push a directory to the runtime environment."""
        if not self._initialized or self._workspace_path is None:
            raise RuntimeInitializationError("Runtime is not initialized")
        try:
            random_branch_name = (
                f"e2b_runtime_{self.config.chainlit_session_id}"
            )
            result = await self.run_command(
                "git-push",
                [REPO_NAME, ".", random_branch_name],
            )
            return result.stdout
        except Exception as e:
            raise RuntimeExecutionError(
                f"Error pushing directory {self._workspace_path}: {e}"
            ) from e

    async def pull_code(self, commit_hash: str, repo_url: str) -> str:
        """Pull the code from the remote repository."""
        if not self._initialized or self._workspace_path is None:
            raise RuntimeInitializationError("Runtime is not initialized")
        try:
            result = await self.run_command(
                "git-pull",
                [REPO_NAME, commit_hash, "."],
            )
            return result.stdout
        except Exception as e:
            raise RuntimeExecutionError(f"Error pulling code: {e}") from e

    def _stream_stdout(self, output: str):
        """Stream command stdout output and propagate as runtime event."""
        # Add to log streamer
        self.log_streamer.add_log("stdout", output)

        info(f"[E2BRuntime]: [stdout] {output}")

    def _stream_stderr(self, output: str):
        """Stream command stderr output and propagate as runtime event."""
        # Add to log streamer
        self.log_streamer.add_log("stderr", output)

        error(f"[E2BRuntime]: [stderr] {output}")

    async def stop(self) -> None:
        """Stop the E2B sandbox and clean up resources."""
        if not self._initialized:
            raise RuntimeInitializationError("Runtime is not initialized")
        try:
            sandbox_id = (
                self._sandbox.sandbox_id if self._sandbox else "unknown"
            )

            if self._sandbox:
                self._sandbox.kill()
            self._sandbox = None
            self._initialized = False

            info(f"[E2BRuntime]: Stopped sandbox {sandbox_id}")

            if self.config.use_browser_session and self._browser_session:
                bb_project_id = os.getenv("BB_PROJECT_ID")
                if bb_project_id:
                    #  Release the browser session.
                    self._browser_base.sessions.update(
                        self._browser_session.id,
                        project_id=bb_project_id,
                        status="REQUEST_RELEASE",
                    )
                    info(
                        f"[E2BRuntime]: Released browser session {self._browser_session.id}"
                    )
                self._browser_session = None
                self._live_view_info = None

            # Clean up browser resources
            await self._cleanup_browser_resources()

            # Stop file watcher
            self.stop_file_watcher()
        except Exception as e:
            raise RuntimeExecutionError(
                f"Error stopping E2B sandbox: {e}"
            ) from e

    def _clean_command_output(self, raw_output: str) -> str:
        if not raw_output:
            return ""

        # Remove ANSI escape sequences (like \x1b[?25h)
        cleaned = re.sub(r"\x1b\[[0-9;?]*[a-zA-Z]", "", raw_output)

        # Remove carriage returns and extra blank lines
        cleaned = cleaned.replace("\r", "").strip()

        # Remove 'spawn ...' if it exists
        lines = cleaned.splitlines()
        if lines and lines[0].startswith("spawn"):
            lines = lines[1:]

        return "\n".join(lines)

    # ------------------------------------------------------------------
    # Browser-session helpers
    # ------------------------------------------------------------------

    async def create_browser_session(
        self, bb_context_id: str | None
    ) -> dict[str, str]:
        """Create a fresh Browserbase session and return connection env vars.

        If a session is already active we proactively release it first so that
        we don't leak remote resources or leave recordings unfinished.
        """

        # If a session already exists, release it gracefully first.
        if self._browser_session is not None:
            try:
                self.release_browser_session()
            except Exception as exc:
                # Swallow errors so that we can still attempt to create a fresh session.
                warn(
                    f"[E2BRuntime]: Failed to release existing browser session before creating a new one: {exc}"
                )

        info("[E2BRuntime]: Setting up browser session...")
        bb_project_id = os.getenv("BB_PROJECT_ID")
        if not bb_project_id:
            raise RuntimeInitializationError(
                "Browserbase project ID is not set (BB_PROJECT_ID environment variable)"
            )
        if not bb_context_id:
            context = self._browser_base.contexts.create(
                project_id=bb_project_id
            )
            bb_context_id = context.id

        # Get proxy configuration based on allowlist
        proxy_config = None
        try:
            proxy_config = await self._get_proxy_configuration()
        except Exception as e:
            warn(f"[E2BRuntime]: Failed to get proxy configuration: {e}")

        # Prepare session creation parameters
        session_params = {
            "project_id": bb_project_id,
            "keep_alive": True,
            "timeout": 3_600,  # 1 hour
            "browser_settings": {
                "context": {"id": bb_context_id, "persist": True}
            },  # 1 hours
        }

        # Add proxy configuration if available
        if proxy_config:
            session_params["proxies"] = proxy_config  # proxy_config
            info(
                f"[E2BRuntime]: Creating browser session with proxy enabled for {len(proxy_config)} domain patterns"
            )
        else:
            info("[E2BRuntime]: Creating browser session without proxy")

        browser_session = self._browser_base.sessions.create(**session_params)
        browser_session_id = browser_session.id
        connection_url = browser_session.connect_url

        # Retrieve the live view (debugger) URL
        live_view_info = self._browser_base.sessions.debug(
            browser_session_id, timeout=60
        )

        if not connection_url or not browser_session_id:
            raise RuntimeInitializationError("Failed to create browser session")

        if not live_view_info:
            warn(
                f"[E2BRuntime]: Timed out to get live view info for browser session {browser_session_id}"
            )

        # Wrap the response into our internal BrowserSession dataclass so that
        # type checkers recognise the expected attributes.
        self._browser_session = BrowserSession(
            id=browser_session_id,
            connect_url=connection_url,
            debugger_url=live_view_info.debugger_url if live_view_info else "",
            context_id=bb_context_id,
        )

        # Store live-view info separately for convenience
        self._live_view_info = (
            live_view_info.debugger_url if live_view_info else None
        )

        # After the explicit assignment above, the attribute is guaranteed non-None.
        assert (
            self._browser_session is not None
        )  # Narrow optional union for type checkers

        info(
            f"[E2BRuntime]: Created browser session {self._browser_session.id}"
        )

        # Return the environment variables that consumers can merge into their own
        # environment dicts so that executed workloads can attach to this session.
        return {"CONNECTION_URL": self._browser_session.connect_url}

    def release_browser_session(self) -> None:
        """Release the current Browserbase session."""
        if not self._browser_session:
            return  # Nothing to do if no session is active

        # Store session ID for logging
        session_id = self._browser_session.id

        try:
            info(f"[E2BRuntime]: Releasing browser session {session_id}...")

            # Request session release first so the recording finalizes.
            bb_project_id = os.getenv("BB_PROJECT_ID")
            if bb_project_id:
                self._browser_base.sessions.update(
                    self._browser_session.id,
                    project_id=bb_project_id,
                    status="REQUEST_RELEASE",
                )
                info(
                    f"[E2BRuntime]: Successfully released browser session {session_id}"
                )
            else:
                warn(
                    "[E2BRuntime]: BB_PROJECT_ID not set while releasing browser session – skipping update call."
                )

        except Exception as e:
            warn(
                f"[E2BRuntime]: Failed to release browser session {session_id}: {e}"
            )
        finally:
            # Always clear the browser session state, even if release fails
            self._browser_session = None
            self._live_view_info = None

            debug(
                f"[E2BRuntime]: Cleared browser session state for session {session_id}"
            )

    # Browser automation methods using Browserbase + Playwright
    async def _get_browser_page(self):
        """Get or create a Playwright page instance for browser automation."""
        if not self.config.use_browser_session or not self._browser_session:
            raise RuntimeExecutionError(
                "Browser session is not available. Enable use_browser_session in config."
            )

        try:
            # Import Playwright inside method to avoid dependency issues
            from playwright.async_api import async_playwright

            # Check if we already have a page instance
            if not hasattr(self, "_playwright_instance") or not hasattr(
                self, "_browser_page"
            ):
                # Create Playwright instance
                self._playwright_instance = await async_playwright().start()

                # Connect to Browserbase session
                browser = (
                    await self._playwright_instance.chromium.connect_over_cdp(
                        self._browser_session.connect_url
                    )
                )

                # Get or create context and page
                if browser.contexts:
                    context = browser.contexts[0]
                    if context.pages:
                        self._browser_page = context.pages[0]
                    else:
                        self._browser_page = await context.new_page()
                else:
                    context = await browser.new_context()
                    self._browser_page = await context.new_page()

                self._browser_context = context

            return self._browser_page

        except ImportError as e:
            raise RuntimeExecutionError(
                "Playwright is not installed. Install with: pip install playwright"
            ) from e
        except Exception as e:
            raise RuntimeExecutionError(
                f"Failed to connect to browser session: {e}"
            ) from e

    async def browser_click(
        self,
        index: int | None = None,
        coordinate_x: float | None = None,
        coordinate_y: float | None = None,
    ) -> str:
        """Click on elements in the current browser page using Playwright."""
        try:
            page = await self._get_browser_page()

            if index is not None:
                # Get clickable elements with their coordinates for more robust interaction
                clickable_elements = await page.evaluate("""
                    () => {
                        const elements = Array.from(document.querySelectorAll('a, button, input[type="button"], input[type="submit"], [onclick], [role="button"]'));
                        const clickableElements = [];
                        
                        elements.forEach((el, idx) => {
                            const rect = el.getBoundingClientRect();
                            const style = window.getComputedStyle(el);
                            
                            if (rect.width > 0 && rect.height > 0 && 
                                style.visibility !== 'hidden' && 
                                style.display !== 'none' && 
                                rect.top >= 0 && rect.left >= 0) {
                                clickableElements.push({
                                    index: idx,
                                    x: rect.left + rect.width / 2,
                                    y: rect.top + rect.height / 2,
                                    width: rect.width,
                                    height: rect.height,
                                    tag: el.tagName.toLowerCase(),
                                    text: (el.textContent || el.value || '').trim().substring(0, 50)
                                });
                            }
                        });
                        
                        return clickableElements;
                    }
                """)

                if index >= len(clickable_elements):
                    # Take screenshot for debugging and save to E2B sandbox
                    screenshot_buffer = await page.screenshot(full_page=True)
                    screenshot_path = "debug_screenshot_click.png"
                    await self.write_file(screenshot_path, screenshot_buffer)

                    raise RuntimeExecutionError(
                        f"Element index {index} out of range. Found {len(clickable_elements)} clickable elements. "
                        f"Screenshot saved to {screenshot_path} for debugging."
                    )

                element_info = clickable_elements[index]

                # Use coordinates for more reliable clicking
                await page.mouse.click(element_info["x"], element_info["y"])

                return f"Clicked {element_info['tag']} element at index {index} ('{element_info['text']}') at coordinates ({element_info['x']}, {element_info['y']})"

            elif coordinate_x is not None and coordinate_y is not None:
                # Click by coordinates - more robust approach
                await page.mouse.click(coordinate_x, coordinate_y)
                return (
                    f"Clicked at coordinates ({coordinate_x}, {coordinate_y})"
                )

            else:
                raise RuntimeExecutionError(
                    "Either index or both coordinate_x and coordinate_y must be provided"
                )

        except Exception as e:
            # Take screenshot on error for debugging
            try:
                page = await self._get_browser_page()
                screenshot_buffer = await page.screenshot(full_page=True)
                screenshot_path = "error_screenshot_click.png"
                await self.write_file(screenshot_path, screenshot_buffer)
            except Exception:
                pass
            raise RuntimeExecutionError(f"Browser click failed: {e}") from e

    async def browser_input(
        self,
        text: str,
        press_enter: bool,
        index: int | None = None,
        coordinate_x: float | None = None,
        coordinate_y: float | None = None,
    ) -> str:
        """Input text into form elements using Playwright."""
        try:
            page = await self._get_browser_page()

            if index is not None:
                # Get input elements with their coordinates and properties
                input_elements = await page.evaluate("""
                    () => {
                        const elements = Array.from(document.querySelectorAll('input, textarea, [contenteditable="true"]'));
                        const inputElements = [];
                        
                        elements.forEach((el, idx) => {
                            const rect = el.getBoundingClientRect();
                            const style = window.getComputedStyle(el);
                            
                            if (rect.width > 0 && rect.height > 0 && 
                                style.visibility !== 'hidden' && 
                                style.display !== 'none' && 
                                rect.top >= 0 && rect.left >= 0) {
                                inputElements.push({
                                    index: idx,
                                    x: rect.left + rect.width / 2,
                                    y: rect.top + rect.height / 2,
                                    width: rect.width,
                                    height: rect.height,
                                    tag: el.tagName.toLowerCase(),
                                    type: el.type || 'text',
                                    placeholder: el.placeholder || '',
                                    value: el.value || '',
                                    isContentEditable: el.contentEditable === 'true'
                                });
                            }
                        });
                        
                        return inputElements;
                    }
                """)

                if index >= len(input_elements):
                    # Take screenshot for debugging and save to E2B sandbox
                    screenshot_buffer = await page.screenshot(full_page=True)
                    screenshot_path = "debug_screenshot_input.png"
                    await self.write_file(screenshot_path, screenshot_buffer)

                    raise RuntimeExecutionError(
                        f"Input element index {index} out of range. Found {len(input_elements)} input elements. "
                        f"Screenshot saved to {screenshot_path} for debugging."
                    )

                element_info = input_elements[index]

                # Click on the element first to focus it
                await page.mouse.click(element_info["x"], element_info["y"])

                # Clear existing content using more robust method
                if element_info["isContentEditable"]:
                    # For contenteditable elements, select all and replace
                    await page.keyboard.press("Control+a")
                    await page.keyboard.type(text)
                else:
                    # For regular inputs, use triple-click to select all, then type
                    await page.mouse.click(
                        element_info["x"], element_info["y"], click_count=3
                    )
                    await page.keyboard.type(text)

                if press_enter:
                    await page.keyboard.press("Enter")

                return f"Input text '{text}' into {element_info['tag']} element at index {index} (type: {element_info['type']}) at coordinates ({element_info['x']}, {element_info['y']}){'and pressed Enter' if press_enter else ''}"

            elif coordinate_x is not None and coordinate_y is not None:
                # Click at coordinates first to focus, then clear and type
                await page.mouse.click(coordinate_x, coordinate_y)

                # Try to select all existing content
                await page.keyboard.press("Control+a")
                await page.keyboard.type(text)

                if press_enter:
                    await page.keyboard.press("Enter")

                return f"Input text '{text}' at coordinates ({coordinate_x}, {coordinate_y}){'and pressed Enter' if press_enter else ''}"

            else:
                raise RuntimeExecutionError(
                    "Either index or both coordinate_x and coordinate_y must be provided"
                )

        except Exception as e:
            # Take screenshot on error for debugging
            try:
                page = await self._get_browser_page()
                screenshot_buffer = await page.screenshot(full_page=True)
                screenshot_path = "error_screenshot_input.png"
                await self.write_file(screenshot_path, screenshot_buffer)
            except Exception:
                pass
            raise RuntimeExecutionError(f"Browser input failed: {e}") from e

    async def browser_move_mouse(
        self, coordinate_x: float, coordinate_y: float
    ) -> str:
        """Move mouse cursor to specified coordinates using Playwright."""
        try:
            page = await self._get_browser_page()
            await page.mouse.move(coordinate_x, coordinate_y)
            return (
                f"Moved mouse to coordinates ({coordinate_x}, {coordinate_y})"
            )

        except Exception as e:
            raise RuntimeExecutionError(
                f"Browser move mouse failed: {e}"
            ) from e

    async def browser_press_key(self, key: str) -> str:
        """Simulate key press using Playwright."""
        try:
            page = await self._get_browser_page()

            # Handle key combinations
            if "+" in key:
                # For combinations like "Control+Enter"
                parts = key.split("+")
                modifiers = []
                main_key = parts[-1]

                for modifier in parts[:-1]:
                    if modifier.lower() in ["control", "ctrl"]:
                        modifiers.append("Control")
                    elif modifier.lower() in ["shift"]:
                        modifiers.append("Shift")
                    elif modifier.lower() in ["alt"]:
                        modifiers.append("Alt")
                    elif modifier.lower() in ["meta", "cmd"]:
                        modifiers.append("Meta")

                # Press with modifiers
                await page.keyboard.press("+".join(modifiers + [main_key]))
            else:
                # Single key press
                await page.keyboard.press(key)

            return f"Pressed key '{key}'"

        except Exception as e:
            raise RuntimeExecutionError(f"Browser press key failed: {e}") from e

    async def browser_select_option(self, index: int, option: int) -> str:
        """Select option from dropdown using Playwright."""
        try:
            page = await self._get_browser_page()

            # Get all select elements
            select_elements = await page.query_selector_all("select")

            if index >= len(select_elements):
                raise RuntimeExecutionError(
                    f"Dropdown index {index} out of range. Found {len(select_elements)} dropdowns."
                )

            select_element = select_elements[index]

            # Get all options
            options = await select_element.query_selector_all("option")

            if option >= len(options):
                raise RuntimeExecutionError(
                    f"Option index {option} out of range. Found {len(options)} options."
                )

            # Select by index
            await select_element.select_option(index=option)

            return f"Selected option {option} from dropdown at index {index}"

        except Exception as e:
            raise RuntimeExecutionError(
                f"Browser select option failed: {e}"
            ) from e

    async def browser_scroll_up(self, to_top: bool | None = None) -> str:
        """Scroll up the page using Playwright."""
        try:
            page = await self._get_browser_page()

            if to_top:
                await page.evaluate("window.scrollTo(0, 0)")
                return "Scrolled to top of page"
            else:
                await page.evaluate("window.scrollBy(0, -window.innerHeight)")
                return "Scrolled up one viewport"

        except Exception as e:
            raise RuntimeExecutionError(f"Browser scroll up failed: {e}") from e

    async def browser_scroll_down(self, to_bottom: bool | None = None) -> str:
        """Scroll down the page using Playwright."""
        try:
            page = await self._get_browser_page()

            if to_bottom:
                await page.evaluate(
                    "window.scrollTo(0, document.body.scrollHeight)"
                )
                return "Scrolled to bottom of page"
            else:
                await page.evaluate("window.scrollBy(0, window.innerHeight)")
                return "Scrolled down one viewport"

        except Exception as e:
            raise RuntimeExecutionError(
                f"Browser scroll down failed: {e}"
            ) from e

    async def browser_console_exec(self, javascript: str) -> str:
        """Execute JavaScript in browser console using Playwright."""
        try:
            page = await self._get_browser_page()
            result = await page.evaluate(javascript)

            # Convert result to string representation
            if result is None:
                return "undefined"
            elif isinstance(result, str | int | float | bool):
                return str(result)
            else:
                # For complex objects, try to serialize as JSON
                try:
                    import json

                    return json.dumps(result, indent=2)
                except (TypeError, ValueError):
                    return str(result)

        except Exception as e:
            raise RuntimeExecutionError(
                f"Browser console exec failed: {e}"
            ) from e

    async def browser_console_view(self, max_lines: int | None = None) -> str:
        """View browser console status and page information using Playwright."""
        try:
            page = await self._get_browser_page()

            # Get comprehensive page information
            page_info = await page.evaluate("""
                () => {
                    const logs = [];
                    logs.push('=== Browser Console Status ===');
                    logs.push('URL: ' + window.location.href);
                    logs.push('Title: ' + document.title);
                    logs.push('Ready State: ' + document.readyState);
                    logs.push('Viewport: ' + window.innerWidth + 'x' + window.innerHeight);
                    logs.push('Scroll Position: ' + window.scrollX + ',' + window.scrollY);
                    logs.push('User Agent: ' + navigator.userAgent);
                    logs.push('Elements Count: ' + document.querySelectorAll('*').length);
                    logs.push('Links Count: ' + document.querySelectorAll('a').length);
                    logs.push('Forms Count: ' + document.querySelectorAll('form').length);
                    logs.push('Images Count: ' + document.querySelectorAll('img').length);
                    
                    // Check for console errors (basic)
                    try {
                        const errorCount = window.console._errors ? window.console._errors.length : 0;
                        logs.push('Console Errors: ' + errorCount);
                    } catch {
                        logs.push('Console Errors: Unable to determine');
                    }
                    
                    return logs.join('\\n');
                }
            """)

            if max_lines:
                lines = page_info.split("\n")[:max_lines]
                result = "\n".join(lines)
                if len(page_info.split("\n")) > max_lines:
                    result += f"\n... (truncated to {max_lines} lines)"
                return result

            return page_info

        except Exception as e:
            raise RuntimeExecutionError(
                f"Browser console view failed: {e}"
            ) from e

    async def browser_view(self) -> str:
        """View content of the current browser page using Playwright."""
        try:
            page = await self._get_browser_page()

            # Take a screenshot first for reference and save to E2B sandbox
            screenshot_path = "current_page_screenshot.png"
            screenshot_saved = False
            try:
                screenshot_buffer = await page.screenshot(full_page=True)
                await self.write_file(screenshot_path, screenshot_buffer)
                screenshot_saved = True
            except Exception as screenshot_error:
                debug(
                    f"Screenshot capture failed: {screenshot_error}, continuing without screenshot"
                )

            # Get comprehensive page content and information with coordinates
            page_content = await page.evaluate("""
                () => {
                    const result = {};
                    
                    // Basic page info
                    result.url = window.location.href;
                    result.title = document.title;
                    result.readyState = document.readyState;
                    
                    // Viewport and scroll info
                    result.viewport = {
                        width: window.innerWidth,
                        height: window.innerHeight,
                        scrollX: window.scrollX,
                        scrollY: window.scrollY
                    };
                    
                    // Interactive elements with coordinates for automation
                    result.interactiveElements = {
                        clickable: Array.from(document.querySelectorAll('a, button, input[type="button"], input[type="submit"], [onclick], [role="button"]')).map((el, idx) => {
                            const rect = el.getBoundingClientRect();
                            const style = window.getComputedStyle(el);
                            
                            if (rect.width > 0 && rect.height > 0 && 
                                style.visibility !== 'hidden' && 
                                style.display !== 'none' && 
                                rect.top >= 0 && rect.left >= 0) {
                                return {
                                    index: idx,
                                    tag: el.tagName.toLowerCase(),
                                    text: (el.textContent || el.value || '').trim().substring(0, 80),
                                    href: el.href || '',
                                    coordinates: {
                                        x: Math.round(rect.left + rect.width / 2),
                                        y: Math.round(rect.top + rect.height / 2),
                                        width: Math.round(rect.width),
                                        height: Math.round(rect.height)
                                    }
                                };
                            }
                            return null;
                        }).filter(el => el !== null).slice(0, 15),
                        
                        inputs: Array.from(document.querySelectorAll('input, textarea, [contenteditable="true"]')).map((el, idx) => {
                            const rect = el.getBoundingClientRect();
                            const style = window.getComputedStyle(el);
                            
                            if (rect.width > 0 && rect.height > 0 && 
                                style.visibility !== 'hidden' && 
                                style.display !== 'none' && 
                                rect.top >= 0 && rect.left >= 0) {
                                return {
                                    index: idx,
                                    tag: el.tagName.toLowerCase(),
                                    type: el.type || 'text',
                                    placeholder: el.placeholder || '',
                                    value: (el.value || '').substring(0, 50),
                                    coordinates: {
                                        x: Math.round(rect.left + rect.width / 2),
                                        y: Math.round(rect.top + rect.height / 2),
                                        width: Math.round(rect.width),
                                        height: Math.round(rect.height)
                                    }
                                };
                            }
                            return null;
                        }).filter(el => el !== null).slice(0, 15),
                        
                        selects: Array.from(document.querySelectorAll('select')).map((el, idx) => {
                            const rect = el.getBoundingClientRect();
                            const style = window.getComputedStyle(el);
                            
                            if (rect.width > 0 && rect.height > 0 && 
                                style.visibility !== 'hidden' && 
                                style.display !== 'none') {
                                return {
                                    index: idx,
                                    options: Array.from(el.options).map((opt, optIdx) => ({
                                        index: optIdx,
                                        text: opt.text.substring(0, 50),
                                        value: opt.value,
                                        selected: opt.selected
                                    })),
                                    coordinates: {
                                        x: Math.round(rect.left + rect.width / 2),
                                        y: Math.round(rect.top + rect.height / 2),
                                        width: Math.round(rect.width),
                                        height: Math.round(rect.height)
                                    }
                                };
                            }
                            return null;
                        }).filter(el => el !== null).slice(0, 10)
                    };
                    
                    // Page content structure
                    result.content = {
                        headings: Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6')).map(h => ({
                            tag: h.tagName.toLowerCase(),
                            text: h.textContent.trim().substring(0, 100)
                        })).slice(0, 10),
                        
                        bodyText: document.body.textContent.trim().substring(0, 800)
                    };
                    
                    // Element counts
                    result.stats = {
                        totalElements: document.querySelectorAll('*').length,
                        links: document.querySelectorAll('a').length,
                        forms: document.querySelectorAll('form').length,
                        images: document.querySelectorAll('img').length,
                        buttons: document.querySelectorAll('button, input[type="button"], input[type="submit"]').length,
                        inputs: document.querySelectorAll('input, textarea, select').length
                    };
                    
                    return result;
                }
            """)

            # Format the result as readable text
            lines = []
            lines.append("=== Browser Page View ===")
            lines.append(f"URL: {page_content['url']}")
            lines.append(f"Title: {page_content['title']}")
            lines.append(f"Ready State: {page_content['readyState']}")
            lines.append(
                f"Viewport: {page_content['viewport']['width']}x{page_content['viewport']['height']}"
            )
            lines.append(
                f"Scroll Position: {page_content['viewport']['scrollX']},{page_content['viewport']['scrollY']}"
            )
            if screenshot_saved:
                lines.append(f"Screenshot saved: {screenshot_path}")
            else:
                lines.append("Screenshot: Failed to capture")
            lines.append("")

            # Page statistics
            stats = page_content["stats"]
            lines.append("=== Page Statistics ===")
            lines.append(f"Total Elements: {stats['totalElements']}")
            lines.append(f"Links: {stats['links']}")
            lines.append(f"Forms: {stats['forms']}")
            lines.append(f"Images: {stats['images']}")
            lines.append(f"Buttons: {stats['buttons']}")
            lines.append(f"Input Fields: {stats['inputs']}")
            lines.append("")

            # Interactive elements with coordinates
            interactive = page_content["interactiveElements"]

            if interactive["clickable"]:
                lines.append("=== Clickable Elements (with coordinates) ===")
                for el in interactive["clickable"]:
                    coord_info = (
                        f"@({el['coordinates']['x']}, {el['coordinates']['y']})"
                    )
                    text_info = f"'{el['text']}'" if el["text"] else "(no text)"
                    href_info = f" -> {el['href']}" if el["href"] else ""
                    lines.append(
                        f"Index {el['index']}: {el['tag']} {coord_info} {text_info}{href_info}"
                    )
                lines.append("")

            if interactive["inputs"]:
                lines.append("=== Input Fields (with coordinates) ===")
                for inp in interactive["inputs"]:
                    coord_info = f"@({inp['coordinates']['x']}, {inp['coordinates']['y']})"
                    value_info = (
                        f", value: '{inp['value']}'" if inp["value"] else ""
                    )
                    placeholder_info = (
                        f", placeholder: '{inp['placeholder']}'"
                        if inp["placeholder"]
                        else ""
                    )
                    lines.append(
                        f"Index {inp['index']}: {inp['tag']}({inp['type']}) {coord_info}{placeholder_info}{value_info}"
                    )
                lines.append("")

            if interactive["selects"]:
                lines.append("=== Select Dropdowns (with coordinates) ===")
                for sel in interactive["selects"]:
                    coord_info = f"@({sel['coordinates']['x']}, {sel['coordinates']['y']})"
                    lines.append(f"Index {sel['index']}: select {coord_info}")
                    for opt in sel["options"][:5]:  # Show first 5 options
                        selected_mark = " (SELECTED)" if opt["selected"] else ""
                        lines.append(
                            f"  Option {opt['index']}: {opt['text']}{selected_mark}"
                        )
                lines.append("")

            # Content structure
            content = page_content["content"]

            if content["headings"]:
                lines.append("=== Headings ===")
                for heading in content["headings"]:
                    lines.append(f"{heading['tag'].upper()}: {heading['text']}")
                lines.append("")

            if content["bodyText"]:
                lines.append("=== Page Text (first 800 chars) ===")
                lines.append(content["bodyText"])

            return "\n".join(lines)

        except Exception as e:
            # Take screenshot on error for debugging
            try:
                page = await self._get_browser_page()
                screenshot_buffer = await page.screenshot(full_page=True)
                error_screenshot_path = "error_screenshot_view.png"
                await self.write_file(error_screenshot_path, screenshot_buffer)
            except Exception:
                pass
            raise RuntimeExecutionError(f"Browser view failed: {e}") from e

    async def browser_navigate(self, url: str) -> str:
        """Navigate browser to specified URL using Playwright."""
        try:
            page = await self._get_browser_page()

            # Validate URL format
            if not url.startswith(("http://", "https://")):
                raise RuntimeExecutionError(
                    f"URL must include protocol prefix (http:// or https://): {url}"
                )

            # Navigate to URL with timeout
            await page.goto(url, timeout=30000, wait_until="domcontentloaded")

            # Get final URL and title
            final_url = page.url
            title = await page.title()

            return f"Navigated to: {final_url}\nPage Title: {title}"

        except Exception as e:
            raise RuntimeExecutionError(f"Browser navigate failed: {e}") from e

    async def browser_restart(self, url: str) -> str:
        """Restart browser and navigate to specified URL using Playwright."""
        try:
            # Validate URL format first
            if not url.startswith(("http://", "https://")):
                raise RuntimeExecutionError(
                    f"URL must include protocol prefix (http:// or https://): {url}"
                )

            # Clean up existing browser resources
            await self._cleanup_browser_resources()

            # Get a fresh browser page (this will recreate the connection)
            page = await self._get_browser_page()

            # Navigate to the specified URL
            await page.goto(url, timeout=30000, wait_until="domcontentloaded")

            # Get final URL and title
            final_url = page.url
            title = await page.title()

            return f"Browser restarted and navigated to: {final_url}\nPage Title: {title}"

        except Exception as e:
            raise RuntimeExecutionError(f"Browser restart failed: {e}") from e

    async def _cleanup_browser_resources(self):
        """Clean up Playwright browser resources."""
        try:
            if hasattr(self, "_browser_page"):
                await self._browser_page.close()
                delattr(self, "_browser_page")

            if hasattr(self, "_browser_context"):
                await self._browser_context.close()
                delattr(self, "_browser_context")

            if hasattr(self, "_browser_base") and self._browser_base:
                self._browser_base.close()
                delattr(self, "_browser_base")

            if hasattr(self, "_browser_session"):
                delattr(self, "_browser_session")

            if hasattr(self, "_playwright_instance"):
                await self._playwright_instance.stop()
                delattr(self, "_playwright_instance")

        except Exception as e:
            debug(f"Error cleaning up browser resources: {e}")
