from __future__ import annotations

from abc import ABC, abstractmethod
from collections.abc import Callable
from dataclasses import dataclass, field
from enum import Enum
import os
from pathlib import Path
from typing import IO

from common.log import info
from common.sandbox.file_watcher import FileWatcher, ProcessedEvent
from common.sandbox.log_streamer import LogStreamer
from web_server.constants.env import (
    MCP_CONFIG_FILE_NAME,
    RUNTIME_GENERATE_MANIFEST_FILE_NAME,
)
from web_server.runtime_init.mcp_config.config_loader import MCPConfigManager


class RuntimeType(Enum):
    LOCAL = "local"
    E2B = "e2b"


@dataclass(frozen=True)
class PreviousRuntimeSessionConfig:
    sandbox_session_id: str | None = None
    browser_session_id: str | None = None
    chainlit_session_id: str | None = None


@dataclass(frozen=True)
class RuntimeConfig(ABC):
    run_type: RuntimeType
    # Chainlit session id
    chainlit_session_id: str
    template: str | None = None
    envs: dict[str, str] = field(default_factory=dict)
    # If true, the runtime will use a browser session to run the code.
    # Currently, only E2B runtime supports this by default.
    use_browser_session: bool = False
    # If true, the runtime will use a previous runtime session config to initialize the runtime and if it fails will fallback to a new runtime session.
    previous_runtime_session_config: PreviousRuntimeSessionConfig | None = None
    workflow_commit_hash: str | None = None
    bb_context_id: str | None = None


@dataclass(frozen=True)
class BrowserSession:
    id: str
    connect_url: str
    debugger_url: str
    context_id: str = ""


@dataclass(frozen=True)
class ExecutionResult:
    """Result of code execution."""

    returncode: int
    stdout: str
    stderr: str


class Runtime(ABC):
    """
    Abstract base class for runtime environments.

    Runtime Directory Structure:
    - Local: {cwd}/runtime_workspace/{chainlit_session_id}/ (session isolation - multi-session support)
    - E2B: /home/<USER>/workspace/{chainlit_session_id} (single session per sandbox instance)

    Each runtime type handles isolation differently:
    - Local: Uses chainlit_session_id in path for multi-session support
    - E2B: Creates separate sandbox instances per session

    PATH HANDLING:
    - Local: Supports both relative and absolute paths with security validation
    - E2B: Only supports relative paths (absolute paths treated as relative)
    - RECOMMENDATION: Always use relative paths for cross-runtime compatibility
    - Avoid path traversal (../) - blocked in Local, unreliable in E2B

    Examples:
      ✅ Good (works in both): "config.json", "src/main.py", "data/input.txt"
      ⚠️  Local only: "/tmp/file.txt", "/home/<USER>/data.json"
      ❌ Avoid: "../file.txt", "../../etc/passwd"
    """

    config: RuntimeConfig

    def __init__(self, config: RuntimeConfig):
        self.config = config
        self._current_execution_id: str | None = None
        # Log streamer for collecting logs
        self.log_streamer = LogStreamer()
        # File watcher for monitoring file changes
        self.file_watcher = FileWatcher()

    @property
    @abstractmethod
    def runtime_type(self) -> RuntimeType:
        """Get the runtime type identifier."""
        pass

    @property
    @abstractmethod
    def runtime_path(self) -> Path:
        """
        Get the path to the runtime workspace directory.
        """
        pass

    @property
    @abstractmethod
    def is_previous_runtime_session_restored(self) -> bool:
        """Get the flag indicating if runtime session was restored from previous runtime session. It will always be false for local runtime. but could be true for E2B runtime."""
        pass

    @abstractmethod
    def get_full_path(self, relative_path: str) -> Path:
        """
        Get the full path based on relative path to the working directory.
        Working directory is in the format of .../runtime_workspace/{chainlit_session}

        Args:
            relative_path: Path relative to the runtime workspace root.
                          Use relative paths for cross-runtime compatibility.
                          Absolute paths only work in Local runtime.

        Returns:
            Full path as Path object

        Raises:
            RuntimeExecutionError: If path is unsafe or invalid
        """
        pass

    @property
    @abstractmethod
    def browser_session(self) -> BrowserSession | None:
        """Get the browser session."""
        pass

    @property
    @abstractmethod
    def sandbox_session_id(self) -> str | None:
        """Get the id of the sandbox session."""
        pass

    async def initialize(self) -> None:
        """Initialize the runtime environment."""
        await self.initialize_internal()
        await self.init()

    @abstractmethod
    async def initialize_internal(self) -> None:
        """Initialize the runtime environment."""
        pass

    @abstractmethod
    async def read_file(self, path: str) -> str:
        """
        Read a file from the runtime workspace.

        Args:
            path: File path relative to the runtime workspace root.
                  Use relative paths for cross-runtime compatibility.
                  Absolute paths only work in Local runtime.

        Returns:
            File content as string

        Raises:
            RuntimeExecutionError: If file not found, permission denied, or path unsafe
        """
        pass

    @abstractmethod
    async def read_bytes(self, path: str) -> bytes:
        """
        Read image data from the runtime workspace.

        Args:
            path: Image file path relative to the runtime workspace root.
                  Use relative paths for cross-runtime compatibility.
                  Absolute paths only work in Local runtime.

        Returns:
            Image data as bytes

        Raises:
            RuntimeExecutionError: If file not found, permission denied, path unsafe, or not an image
        """
        pass

    @abstractmethod
    async def write_file(self, path: str, content: str | bytes | IO) -> None:
        """
        Write content to a file in the runtime workspace.

        Args:
            path: File path relative to the runtime workspace root.
                  Use relative paths for cross-runtime compatibility.
                  Absolute paths only work in Local runtime.
            content: Content to write (string, bytes, or IO object)

        Raises:
            RuntimeExecutionError: If permission denied, path unsafe, or write fails
        """
        pass

    @abstractmethod
    async def run_command(
        self,
        command: str,
        args: list[str],
        work_dir: str | None = None,
        envs: dict[str, str] | None = None,
    ) -> ExecutionResult:
        """
        Execute a command in the runtime environment.

        Args:
            command: Command to execute
            args: Command arguments
            work_dir: Working directory path relative to runtime workspace root.
                     Use relative paths for cross-runtime compatibility.
                     Absolute paths only work in Local runtime.
                     None: Uses runtime workspace root as working directory

        Returns:
            ExecutionResult with returncode, stdout, stderr

        Raises:
            RuntimeExecutionError: If command fails, permission denied, or path unsafe
        """
        pass

    @abstractmethod
    async def run_bash_command(
        self,
        command: str,
        work_dir: str | None = None,
        envs: dict[str, str] | None = None,
    ) -> ExecutionResult:
        """
        Execute a bash command in the runtime environment.

        Args:
            command: Bash command string to execute
            work_dir: Working directory path relative to runtime workspace root.
                     Use relative paths for cross-runtime compatibility.
                     Absolute paths only work in Local runtime.
                     None: Uses runtime workspace root as working directory
            envs: Environment variables to set for command execution

        Returns:
            ExecutionResult with returncode, stdout, stderr

        Raises:
            RuntimeExecutionError: If command fails, permission denied, or path unsafe
        """
        pass

    @abstractmethod
    async def push_workspace(self) -> str:
        """Push the runtime workspace to the remote repository."""
        pass

    @abstractmethod
    async def pull_code(self, commit_hash: str, repo_url: str) -> str:
        """Pull the code from the remote repository.

        Args:
            commit_hash: The commit hash to pull.
        """
        pass

    @abstractmethod
    async def run_code(
        self,
        work_dir: str | None = None,
        entry_point: str = "main.py",
        args: list[str] | None = None,
        envs: dict[str, str] | None = None,
    ) -> ExecutionResult:
        """
        Run Python code in the runtime environment.

        Args:
            work_dir: Working directory path relative to runtime workspace root.
                     Use relative paths for cross-runtime compatibility.
                     Absolute paths only work in Local runtime.
                     None: Uses runtime workspace root as working directory
            entry_point: Python file to execute (default: "main.py")

        Returns:
            ExecutionResult with returncode, stdout, stderr

        Raises:
            RuntimeExecutionError: If entry_point not found, execution fails, or path unsafe
        """
        pass

    @abstractmethod
    async def stop(self) -> None:
        """Stop and clean up the runtime environment."""
        pass

    @abstractmethod
    def start_file_watcher(
        self, file_event_callback: Callable[[ProcessedEvent], None]
    ) -> None:
        """Start the file watcher for the runtime environment."""
        pass

    # Browser interaction methods
    @abstractmethod
    async def browser_click(
        self,
        index: int | None = None,
        coordinate_x: float | None = None,
        coordinate_y: float | None = None,
    ) -> str:
        """
        Click on elements in the current browser page.

        Args:
            index: Index number of the element to click
            coordinate_x: X coordinate of click position
            coordinate_y: Y coordinate of click position

        Returns:
            Result message describing the click action

        Raises:
            RuntimeExecutionError: If browser operation fails or not supported
        """
        pass

    @abstractmethod
    async def browser_input(
        self,
        text: str,
        press_enter: bool,
        index: int | None = None,
        coordinate_x: float | None = None,
        coordinate_y: float | None = None,
    ) -> str:
        """
        Overwrite text in editable elements on the current browser page.

        Args:
            text: Complete text content to overwrite
            press_enter: Whether to press Enter key after input
            index: Index number of the element to overwrite text
            coordinate_x: X coordinate of the element to overwrite text
            coordinate_y: Y coordinate of the element to overwrite text

        Returns:
            Result message describing the input action

        Raises:
            RuntimeExecutionError: If browser operation fails or not supported
        """
        pass

    @abstractmethod
    async def browser_move_mouse(
        self, coordinate_x: float, coordinate_y: float
    ) -> str:
        """
        Move cursor to specified position on the current browser page.

        Args:
            coordinate_x: X coordinate of target cursor position
            coordinate_y: Y coordinate of target cursor position

        Returns:
            Result message describing the mouse movement

        Raises:
            RuntimeExecutionError: If browser operation fails or not supported
        """
        pass

    @abstractmethod
    async def browser_press_key(self, key: str) -> str:
        """
        Simulate key press in the current browser page.

        Args:
            key: Key name to simulate (e.g., Enter, Tab, ArrowUp),
                 supports key combinations (e.g., Control+Enter)

        Returns:
            Result message describing the key press

        Raises:
            RuntimeExecutionError: If browser operation fails or not supported
        """
        pass

    @abstractmethod
    async def browser_select_option(self, index: int, option: int) -> str:
        """
        Select specified option from dropdown list element.

        Args:
            index: Index number of the dropdown list element
            option: Option number to select, starting from 0

        Returns:
            Result message describing the selection

        Raises:
            RuntimeExecutionError: If browser operation fails or not supported
        """
        pass

    @abstractmethod
    async def browser_scroll_up(self, to_top: bool | None = None) -> str:
        """
        Scroll up the current browser page.

        Args:
            to_top: Whether to scroll directly to page top instead of one viewport up

        Returns:
            Result message describing the scroll action

        Raises:
            RuntimeExecutionError: If browser operation fails or not supported
        """
        pass

    @abstractmethod
    async def browser_scroll_down(self, to_bottom: bool | None = None) -> str:
        """
        Scroll down the current browser page.

        Args:
            to_bottom: Whether to scroll directly to page bottom instead of one viewport down

        Returns:
            Result message describing the scroll action

        Raises:
            RuntimeExecutionError: If browser operation fails or not supported
        """
        pass

    @abstractmethod
    async def browser_console_exec(self, javascript: str) -> str:
        """
        Execute JavaScript code in browser console.

        Args:
            javascript: JavaScript code to execute

        Returns:
            Result of the JavaScript execution

        Raises:
            RuntimeExecutionError: If browser operation fails or not supported
        """
        pass

    @abstractmethod
    async def browser_console_view(self, max_lines: int | None = None) -> str:
        """
        View browser console output.

        Args:
            max_lines: Maximum number of log lines to return

        Returns:
            Console output logs

        Raises:
            RuntimeExecutionError: If browser operation fails or not supported
        """
        pass

    @abstractmethod
    async def browser_view(self) -> str:
        """
        View content of the current browser page.

        Returns:
            Current page content and status information

        Raises:
            RuntimeExecutionError: If browser operation fails or not supported
        """
        pass

    @abstractmethod
    async def browser_navigate(self, url: str) -> str:
        """
        Navigate browser to specified URL.

        Args:
            url: Complete URL to visit. Must include protocol prefix.

        Returns:
            Result message describing the navigation

        Raises:
            RuntimeExecutionError: If browser operation fails or not supported
        """
        pass

    @abstractmethod
    async def browser_restart(self, url: str) -> str:
        """
        Restart browser and navigate to specified URL.

        Args:
            url: Complete URL to visit after restart. Must include protocol prefix.

        Returns:
            Result message describing the restart and navigation

        Raises:
            RuntimeExecutionError: If browser operation fails or not supported
        """
        pass

    @abstractmethod
    async def create_browser_session(
        self, bb_context_id: str | None
    ) -> dict[str, str]:
        """
        Create (or renew) a browser session for browser automation.

        Returns:
            dict[str, str]: Environment variables (e.g. ``{"CONNECTION_URL": "ws://..."}``)
            that should be injected into subsequent workload executions. Runtimes that do
            not expose any variables should return an empty dict.
        """
        pass

    @abstractmethod
    def release_browser_session(self) -> None:
        """
        Release/cleanup the current browser session if one is active.
        Runtimes that do not support browser sessions should implement this as
        a harmless no-op so that callers can always invoke the method safely.
        """
        pass

    async def init(self):
        # TODO:Skip init for tests, figure out a better way to do this
        if os.environ.get("PYTEST_RUNNING"):
            return
        # Get the tools from the agent
        mcp_server_manager = MCPConfigManager()
        if self.config.run_type == RuntimeType.E2B and self.browser_session:
            mcp_server_manager.add_server_args(
                "playwright",
                ["--cdp-endpoint", self.browser_session.connect_url],
            )
        else:
            # If the runtime is local, we need to add the isolated flag to the playwright server
            mcp_server_manager.add_server_args("playwright", ["--isolated"])
        json_string = mcp_server_manager.export_to_json()

        # We will write the json string to a file in the runtime workspace
        info(
            f"Writing MCP config to {self.runtime_path / MCP_CONFIG_FILE_NAME}"
        )
        await self.write_file(MCP_CONFIG_FILE_NAME, json_string)

        # Copy the runtime_generate_manifest.py to the runtime workspace
        with open(
            str(
                Path.cwd()
                / "packages"
                / "web_server"
                / "web_server"
                / "runtime_init"
                / RUNTIME_GENERATE_MANIFEST_FILE_NAME
            )
        ) as f:
            info(
                f"Writing runtime_generate_manifest.py to {self.runtime_path / RUNTIME_GENERATE_MANIFEST_FILE_NAME}"
            )
            await self.write_file(RUNTIME_GENERATE_MANIFEST_FILE_NAME, f.read())

        # We will also copy all the tools to the runtime workspace
        # Get list of tools from directory
        tools_dir = (
            Path.cwd()
            / "packages"
            / "web_server"
            / "web_server"
            / "runtime_init"
            / "tools"
        )
        mcp_tools = [f for f in os.listdir(tools_dir) if f.endswith(".py")]

        # Copy each tool
        for tool_name in mcp_tools:
            with open(str(tools_dir / tool_name)) as f:
                info(f"Writing {tool_name} to {self.runtime_path / tool_name}")
                await self.write_file(tool_name, f.read())

    # ---------------------------------------------------------------------
    # Execution identifier helper
    # ---------------------------------------------------------------------

    def set_current_execution_id(self, execution_id: str | None) -> None:
        """Set the execution-id that should be attached to subsequently emitted events."""
        self._current_execution_id = execution_id


class RuntimeError(Exception):
    """Base exception for runtime errors."""

    pass


class RuntimeInitializationError(RuntimeError):
    """Exception raised when runtime initialization fails."""

    pass


class RuntimeExecutionError(RuntimeError):
    """Exception raised when action execution fails."""

    pass


class NotFoundError(RuntimeError):
    """Exception raised if file is not found"""

    pass
