"""
Utility functions for converting User domain models to protobuf UserProfileInfo messages.
"""

from common.models.user import User
from common.user_profile_pb2 import UserProfileInfo


def user_to_profile_info(user: User | None) -> UserProfileInfo | None:
    """
    Convert a User domain model to UserProfileInfo protobuf message.

    Args:
        user: User domain model instance, can be None

    Returns:
        UserProfileInfo protobuf message or None if user is None
    """
    if user is None:
        return None

    # Use email as username (consistent with current auth patterns)
    username = user.email or ""

    # Build full name from first_name and last_name, fallback to full_name field
    full_name = user.full_name or ""
    if not full_name and (user.first_name or user.last_name):
        full_name = f"{user.first_name or ''} {user.last_name or ''}".strip()

    return UserProfileInfo(
        id=str(user.id),
        username=username,
        full_name=full_name,
        image_url=user.image_url or "",
    )
