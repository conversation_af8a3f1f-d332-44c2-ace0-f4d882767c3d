from bson import ObjectId
from google.protobuf.struct_pb2 import Struct
from orby.va.public.execution_messages_pb2 import (
    ExecutionLog as ExecutionLogProto,
)
from orby.va.public.execution_messages_pb2 import (
    ExecutionStatus as ExecutionStatusProto,
)

from common.models.execution import ExecutionLog, ExecutionStatus


def convert_execution_log_model_to_proto(
    execution_log: ExecutionLog,
) -> ExecutionLogProto:
    proto_execution_log = ExecutionLogProto(
        id=str(execution_log.id),
        execution_id=str(execution_log.execution_id),
        workflow_id=str(execution_log.workflow_id),
        org_id=str(execution_log.org_id),
        step_id=execution_log.step_id,
        description=execution_log.description,
        screenshot=execution_log.screenshot,
        status=convert_execution_status_model_to_proto(execution_log.status),
    )

    # Convert metadata dict to protobuf Struct
    metadata_struct = None
    if execution_log.metadata is not None:
        metadata_struct = Struct()
        metadata_struct.update(execution_log.metadata)

    if metadata_struct:
        proto_execution_log.metadata = metadata_struct

    return proto_execution_log


def convert_execution_status_model_to_proto(
    status: ExecutionStatus,
) -> ExecutionStatusProto:
    match status:
        case ExecutionStatus.PENDING:
            return ExecutionStatusProto.PENDING
        case ExecutionStatus.RUNNING:
            return ExecutionStatusProto.RUNNING
        case ExecutionStatus.COMPLETED:
            return ExecutionStatusProto.COMPLETED
        case ExecutionStatus.FAILED:
            return ExecutionStatusProto.FAILED
        case ExecutionStatus.CANCELLED:
            return ExecutionStatusProto.CANCELLED
        case ExecutionStatus.TIMEOUT:
            return ExecutionStatusProto.TIMEOUT
        case _:
            return ExecutionStatusProto.EXECUTION_STATUS_UNSPECIFIED


def convert_execution_status_proto_to_model(
    status: ExecutionStatusProto,
) -> ExecutionStatus | None:
    match status:
        case ExecutionStatusProto.PENDING:
            return ExecutionStatus.PENDING
        case ExecutionStatusProto.RUNNING:
            return ExecutionStatus.RUNNING
        case ExecutionStatusProto.COMPLETED:
            return ExecutionStatus.COMPLETED
        case ExecutionStatusProto.FAILED:
            return ExecutionStatus.FAILED
        case ExecutionStatusProto.CANCELLED:
            return ExecutionStatus.CANCELLED
        case ExecutionStatusProto.TIMEOUT:
            return ExecutionStatus.TIMEOUT
        case _:
            return None


def convert_execution_log_proto_to_model(
    proto_execution_log: ExecutionLogProto,
) -> ExecutionLog:
    if not proto_execution_log.id or not ObjectId.is_valid(
        proto_execution_log.id
    ):
        raise ValueError("Invalid Log ID")
    log_id = ObjectId(proto_execution_log.id)
    if not proto_execution_log.execution_id or not ObjectId.is_valid(
        proto_execution_log.execution_id
    ):
        raise ValueError("Invalid Execution ID")
    execution_id = ObjectId(proto_execution_log.execution_id)
    if not proto_execution_log.workflow_id or not ObjectId.is_valid(
        proto_execution_log.workflow_id
    ):
        raise ValueError("Invalid Workflow ID")
    workflow_id = ObjectId(proto_execution_log.workflow_id)
    if not proto_execution_log.org_id or not ObjectId.is_valid(
        proto_execution_log.org_id
    ):
        raise ValueError("Invalid Org ID")
    org_id = ObjectId(proto_execution_log.org_id)

    execution_log = ExecutionLog(
        _id=log_id,
        execution_id=execution_id,
        workflow_id=workflow_id,
        org_id=org_id,
        step_id=proto_execution_log.step_id,
        description=proto_execution_log.description,
        screenshot=proto_execution_log.screenshot,
        status=convert_execution_status_proto_to_model(
            proto_execution_log.status
        ),
        metadata=dict(proto_execution_log.metadata),
    )

    return execution_log
