"""
Utility functions for converting Workflow domain models to protobuf messages.
"""

from google.protobuf.json_format import ParseDict
from orby.va.workflow_pb2 import Workflow

from common.models.user import User
from common.models.workflow import Workflow as WorkflowModel
from common.models.workflow import WorkflowStatus
from common.utils.user_profile_mapper import user_to_profile_info


def convert_workflow_model_to_proto(
    workflow: WorkflowModel, user_info: User | None = None
) -> Workflow:
    """
    Convert domain workflow model to protobuf message.

    Args:
        workflow: Domain workflow model
        user_info: Optional user info for creator field

    Returns:
        Workflow protobuf message
    """
    # Convert workflow model to dict using model_dump
    workflow_dict = workflow.model_dump(mode="json", by_alias=False)

    # Remove fields that don't exist in the protobuf or need special handling
    workflow_dict.pop("thread_id", None)
    workflow_dict.pop("bb_context_id", None)
    workflow_dict.pop("creator_id", None)  # We'll handle creator separately
    workflow_dict.pop(
        "status", None
    )  # We'll handle status enum mapping manually

    # Convert to protobuf using ParseDict
    proto_workflow = ParseDict(workflow_dict, Workflow())

    # Handle status enum mapping manually

    if workflow.status == WorkflowStatus.DRAFT:
        proto_workflow.status = Workflow.Status.STATUS_DRAFT
    elif workflow.status == WorkflowStatus.COMPLETED:
        proto_workflow.status = Workflow.Status.STATUS_COMPLETED
    else:
        proto_workflow.status = Workflow.Status.STATUS_UNSPECIFIED

    # Add creator info if available
    if user_info:
        user_profile = user_to_profile_info(user_info)
        if user_profile:
            proto_workflow.creator.CopyFrom(user_profile)

    return proto_workflow
