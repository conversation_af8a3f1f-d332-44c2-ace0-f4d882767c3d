"""
Utility functions for converting Workflow domain models to protobuf messages.
"""

from common.models.user import User
from common.models.workflow import Workflow as WorkflowModel
from common.utils.user_profile_mapper import user_to_profile_info
from orby.va.workflow_pb2 import Workflow


def workflow_model_to_proto(
    workflow: WorkflowModel, user_info: User | None = None
) -> Workflow:
    """
    Convert domain workflow model to protobuf message.

    Args:
        workflow: Domain workflow model
        user_info: Optional user info for creator field

    Returns:
        Workflow protobuf message
    """
    proto_workflow = Workflow(
        id=str(workflow.id),
        display_name=workflow.display_name or "",
        org_id=str(workflow.org_id),
    )

    # Add creator info if available
    if user_info:
        user_profile = user_to_profile_info(user_info)
        if user_profile:
            proto_workflow.creator.CopyFrom(user_profile)

    # Add timestamps
    if workflow.created_at:
        proto_workflow.created_at.FromDatetime(workflow.created_at)
    if workflow.updated_at:
        proto_workflow.updated_at.FromDatetime(workflow.updated_at)

    # Add git repository info if available
    if workflow.git_repository:
        proto_workflow.git_repository.repo_url = (
            workflow.git_repository.repo_url or ""
        )
        proto_workflow.git_repository.commit_hash = (
            workflow.git_repository.commit_hash or ""
        )
        if workflow.git_repository.branch:
            proto_workflow.git_repository.branch = (
                workflow.git_repository.branch
            )

    # Add generated files info if available
    if workflow.generated_files:
        if workflow.generated_files.sop_file_id:
            proto_workflow.generated_files.sop_file_id = str(
                workflow.generated_files.sop_file_id
            )
        if workflow.generated_files.manifest_file_id:
            proto_workflow.generated_files.manifest_file_id = str(
                workflow.generated_files.manifest_file_id
            )

    # Add status (map from domain enum to proto enum)
    if hasattr(workflow, "status"):
        if workflow.status.value == "draft":
            proto_workflow.status = Workflow.Status.STATUS_DRAFT
        elif workflow.status.value == "completed":
            proto_workflow.status = Workflow.Status.STATUS_COMPLETED
        else:
            proto_workflow.status = Workflow.Status.STATUS_UNSPECIFIED

    return proto_workflow
