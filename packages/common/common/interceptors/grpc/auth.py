from collections.abc import Callable
from contextvars import ContextVar

import grpc

from common.log import error
from common.utils.auth import AuthPayload
from execution.utils.auth import get_auth_payload_wrapper

auth_payload_context: ContextVar[AuthPayload | None] = ContextVar(
    "auth_payload_context", default=None
)


def get_and_validate_auth_payload() -> AuthPayload:
    auth_payload = auth_payload_context.get()
    print(" ------------------------ get_and_validate_auth_payload  ------------------------ ")
    print(auth_payload)
    if not auth_payload.org_id:
        raise ValueError("No organization ID provided")
    if not auth_payload.user_id:
        raise ValueError("No user ID provided")
    return auth_payload


class AuthInterceptor(grpc.aio.ServerInterceptor):
    """gRPC server interceptor for authentication."""

    def __init__(self):
        pass

    async def intercept_service(
        self,
        continuation: Callable,
        handler_call_details: grpc.HandlerCallDetails,
    ):
        """Main interceptor method - validates auth before calling handlers."""
        auth_payload: AuthPayload | None = None
        try:
            auth_payload = get_auth_payload_wrapper(
                handler_call_details.invocation_metadata
            )
        except Exception as e:
            error_msg = str(e)
            error(f"Error in AuthInterceptor: {error_msg}")

            def abort_handler(_, context: grpc.aio.ServicerContext):
                context.abort(
                    grpc.StatusCode.UNAUTHENTICATED, f"Reason: {error_msg}"
                )

            return grpc.unary_unary_rpc_method_handler(abort_handler)

        auth_payload_context.set(auth_payload)
        return await continuation(handler_call_details)
