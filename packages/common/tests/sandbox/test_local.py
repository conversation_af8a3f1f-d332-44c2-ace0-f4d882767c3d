import asyncio
import os
from pathlib import Path

import pytest

from common.sandbox.base import (
    ExecutionResult,
    Runtime,
    RuntimeConfig,
    RuntimeExecutionError,
    RuntimeType,
)
from common.sandbox.impl.local import LocalRuntime


@pytest.fixture
def runtime_config() -> RuntimeConfig:
    return RuntimeConfig(
        chainlit_session_id="test_session",
        run_type=RuntimeType.LOCAL,
        envs={
            "API_KEY": "test_api_key",
        },
    )


@pytest.fixture
def runtime(runtime_config: RuntimeConfig) -> Runtime:
    return LocalRuntime(runtime_config)


def test_initialize(tmp_path, monkeypatch, runtime, runtime_config):
    # Replace Path.cwd() → tmp_path
    monkeypatch.setattr(Path, "cwd", lambda: tmp_path)

    async def run_test():
        await runtime.initialize()

        expected_path = (
            tmp_path / "runtime_workspace" / runtime_config.chainlit_session_id
        )
        assert runtime.runtime_path == expected_path
        print(f"🔍 Runtime path: {runtime.runtime_path}")
        assert expected_path.exists()

        # Check if the environment variables are set
        assert os.getenv("API_KEY") == "test_api_key"

        # Clean up
        await runtime.stop()
        assert not expected_path.exists()

    asyncio.run(run_test())


@pytest.mark.parametrize(
    "file_path, should_create, expected_content, expected_error",
    [
        ("valid.txt", True, "hello", None),  # Valid file
        ("./valid.txt", True, "hello", None),  # Valid absolute file
        ("abc/test.txt", True, "hello", None),  # Valid Nested file
        ("does_not_exist.txt", False, None, "File not found"),  # Missing file
        (
            "../tmp/test.txt",
            False,
            None,
            "Permission denied",
        ),  # Permission denied, when trying to access  file in root
    ],
    ids=["valid", "valid_absolute", "nested", "not_found", "permission_denied"],
)
def test_read_file(
    tmp_path,
    monkeypatch,
    runtime,
    file_path,
    should_create,
    expected_content,
    expected_error,
):
    async def run_test():
        # Replace Path.cwd() → tmp_path
        monkeypatch.setattr(Path, "cwd", lambda: tmp_path)
        # Initialize the runtime first
        await runtime.initialize()

        runtime_path = runtime.runtime_path
        # Put the file inside the runtime directory
        if should_create:
            full_file_path = runtime_path / file_path
            # Create parent directories if they don't exist (like mkdir -p)
            full_file_path.parent.mkdir(parents=True, exist_ok=True)
            full_file_path.write_text(expected_content)
            assert full_file_path.exists()

        if expected_error:
            with pytest.raises(RuntimeExecutionError) as e:
                await runtime.read_file(file_path)
            assert expected_error in str(e.value)
            return

        # Read the file using the runtime
        content = await runtime.read_file(file_path)
        assert content == expected_content

        # Clean up
        await runtime.stop()
        # Check that the full file path no longer exists
        assert not full_file_path.exists()
        assert not runtime_path.exists()

    asyncio.run(run_test())


@pytest.mark.parametrize(
    "file_path, write_content, expected_string, expected_error",
    [
        ("valid.txt", "hello", "hello", None),  # Valid string content
        (
            "unicode.txt",
            "Hello 世界! 🌍",
            "Hello 世界! 🌍",
            None,
        ),  # Unicode content
        (
            "../tmp/test.txt",
            "hello",
            None,
            "Permission denied",
        ),  # Permission denied, when trying to write outside of runtime directory
        ("write_bytes.txt", b"hello", "hello", None),  # Write bytes content
    ],
    ids=["valid_string", "unicode", "permission_denied", "write_bytes"],
)
def test_write_file(
    tmp_path,
    monkeypatch,
    runtime,
    file_path,
    write_content: str,
    expected_string,
    expected_error,
):
    async def run_test():
        # Replace Path.cwd() → tmp_path
        monkeypatch.setattr(Path, "cwd", lambda: tmp_path)
        # Initialize the runtime first
        await runtime.initialize()

        runtime_path = runtime.runtime_path
        if expected_error:
            with pytest.raises(RuntimeExecutionError) as e:
                await runtime.write_file(file_path, write_content)
            print(f"🔍 Error: {e.value}")
            assert expected_error in str(e.value)
            return

        # Write the file using the runtime
        await runtime.write_file(file_path, write_content)

        # Read back the content to verify it was written correctly
        content = await runtime.read_file(file_path)
        assert content == expected_string

        # Verify the file exists on disk
        full_file_path = (runtime_path) / file_path
        assert full_file_path.exists()

        # Clean up
        await runtime.stop()
        # Check that the full file path no longer exists after cleanup
        assert not full_file_path.exists()
        assert not runtime_path.exists()

    asyncio.run(run_test())


@pytest.mark.parametrize(
    "command, args, expected_result, create_file, work_dir, expected_error",
    [
        (
            "echo",
            ["Hello, World!"],
            ExecutionResult(returncode=0, stdout="Hello, World!", stderr=""),
            None,
            None,
            None,
        ),  # Valid shell command
        (
            "cat",
            ["test.txt"],
            ExecutionResult(returncode=0, stdout="Hello, World!", stderr=""),
            True,
            "abc",
            None,
        ),  # Valid shell command with work_dir
        (
            "echo",
            ["Hello, World!"],
            ExecutionResult(returncode=0, stdout="Hello, World!", stderr=""),
            None,
            "../invalid_work_dir",
            "Permission denied",
        ),  # Invalid work directory
        (
            "invalid",
            [],
            None,
            None,
            None,
            "Error running subprocess",
        ),  # Invalid subprocess
        (
            "false",
            [],
            ExecutionResult(returncode=1, stdout="", stderr=""),
            None,
            None,
            None,
        ),  # Process returns error code
    ],
    ids=[
        "valid_echo",
        "valid_cat",
        "invalid_work_dir",
        "invalid_subprocess",
        "error_code",
    ],
)
def test_run_command(
    tmp_path,
    monkeypatch,
    runtime,
    command,
    args,
    expected_result: ExecutionResult,
    create_file,
    work_dir,
    expected_error,
):
    async def run_test():
        # Replace Path.cwd() → tmp_path
        monkeypatch.setattr(Path, "cwd", lambda: tmp_path)
        # Initialize the runtime first
        await runtime.initialize()

        if create_file:
            full_file_path = runtime.runtime_path / work_dir / "test.txt"
            full_file_path.parent.mkdir(parents=True, exist_ok=True)
            full_file_path.write_text(expected_result.stdout)
            assert full_file_path.exists()

        if expected_error:
            with pytest.raises(RuntimeExecutionError) as e:
                await runtime.run_command(command, args, work_dir)
            assert expected_error in str(e.value)
            return

        # Generate code using the runtime
        result = await runtime.run_command(command, args, work_dir)
        assert result.returncode == expected_result.returncode
        assert result.stdout.strip() == expected_result.stdout.strip()
        assert expected_result.stderr.strip() in result.stderr.strip()

        # Clean up
        await runtime.stop()

    asyncio.run(run_test())


def test_stop(tmp_path, monkeypatch, runtime):
    async def run_test():
        # Replace Path.cwd() → tmp_path
        monkeypatch.setattr(Path, "cwd", lambda: tmp_path)
        # Initialize the runtime first
        await runtime.initialize()
        assert runtime.runtime_path.exists()
        runtime_path = runtime.runtime_path
        await runtime.stop()
        assert not runtime_path.exists()

    asyncio.run(run_test())


@pytest.mark.parametrize(
    "main_py_content, expected_stdout, work_dir, expected_error",
    [
        (
            'print("Hello from main.py!")',
            "Hello from main.py!",
            None,
            None,
        ),  # Valid python code
        (
            'import sys\nprint(f"Working directory: {sys.argv[0]}")',
            "Working directory:",
            None,
            None,
        ),  # Python with sys import
        (
            None,  # No main.py file
            None,
            None,
            "main.py not found",
        ),  # Missing main.py
    ],
    ids=["valid_python", "valid_python_with_sys", "missing_main_py"],
)
def test_run_code(
    tmp_path,
    monkeypatch,
    runtime,
    main_py_content,
    expected_stdout,
    work_dir,
    expected_error,
):
    async def run_test():
        # Replace Path.cwd() → tmp_path
        monkeypatch.setattr(Path, "cwd", lambda: tmp_path)
        # Initialize the runtime first
        await runtime.initialize()

        if main_py_content:
            # Create a main.py file
            await runtime.write_file("main.py", main_py_content)

            # Create a simple requirements.txt (optional)
            requirements_content = "# No requirements for this test"
            await runtime.write_file("requirements.txt", requirements_content)

        if expected_error:
            with pytest.raises(RuntimeExecutionError, match=expected_error):
                await runtime.run_code(work_dir)
            return

        # Run the code
        result = await runtime.run_code(work_dir)

        # Verify the result
        assert result.returncode == 0
        assert expected_stdout in result.stdout

        # Clean up
        await runtime.stop()

    asyncio.run(run_test())


def test_resolve_safe_path(tmp_path, monkeypatch):
    async def run_test():
        # Replace Path.cwd() → tmp_path
        monkeypatch.setattr(Path, "cwd", lambda: tmp_path)
        runtime = LocalRuntime(
            RuntimeConfig(
                chainlit_session_id="test_session", run_type=RuntimeType.LOCAL
            )
        )
        await runtime.initialize()
        assert runtime.resolve_safe_path("valid.txt") == Path(
            "valid.txt"
        )  # Valid file
        assert runtime.resolve_safe_path("./valid.txt") == Path("valid.txt")
        assert runtime.resolve_safe_path(
            str(tmp_path / runtime.runtime_path / "valid.txt")
        ) == Path("valid.txt")

    asyncio.run(run_test())
