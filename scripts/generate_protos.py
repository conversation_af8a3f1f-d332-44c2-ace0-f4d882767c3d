# /// script
# requires-python = ">=3.13"
# dependencies = []
# ///

"""Generate protobuf files from .proto definitions."""

from pathlib import Path
import shutil
import subprocess
import sys


def find_proto_files(proto_dir: Path) -> list[str]:
    """Find all .proto files in the given directory."""
    return [str(p) for p in proto_dir.rglob("*.proto")]


def generate_protos():
    """Generate protobuf files."""

    # Paths
    proto_root = Path("protos")
    # Only generate protos for the orby package plus specific common files
    input_proto_dir = proto_root / "orby"
    specific_proto_files = [proto_root / "common/user_profile.proto"]
    out_dir = Path("packages/protos_gen/protos_gen")

    # Clear previous generated files if the output directory exists; otherwise, create it.
    if out_dir.exists():
        for item in out_dir.iterdir():
            if item.is_dir():
                shutil.rmtree(item)
            else:
                item.unlink()
    else:
        out_dir.mkdir(parents=True, exist_ok=True)

    # Find all proto files
    proto_files = find_proto_files(input_proto_dir)

    # Add specific proto files
    proto_files.extend([str(f) for f in specific_proto_files])

    if not proto_files:
        print("No .proto files found")
        return

    print(f"Found {len(proto_files)} proto files")

    # Build protoc command
    cmd = [
        sys.executable,
        "-m",
        "grpc_tools.protoc",
        f"--proto_path={proto_root}",
        f"--python_out={out_dir}",
        f"--grpc_python_out={out_dir}",
        f"--mypy_out={out_dir}",
        *proto_files,
    ]

    print("Generating protobuf files...")
    result = subprocess.run(cmd, check=False)

    if result.returncode == 0:
        print("✅ Protobuf generation completed successfully!")
    else:
        print("❌ Protobuf generation failed")
        sys.exit(1)


if __name__ == "__main__":
    generate_protos()
