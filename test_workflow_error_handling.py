#!/usr/bin/env python3
"""
Test script to verify improved error handling in workflow operations.
This script tests various invalid input scenarios to ensure proper error messages.
"""

import asyncio
from unittest.mock import Mock, AsyncMock
import grpc
from bson import ObjectId

# Mock the dependencies
import sys
sys.path.append('packages/execution')
sys.path.append('packages/common')

from execution.v1.handlers.grpc.workflow_handler import WorkflowHandler
from orby.va.workflow_pb2 import GetWorkflowRequest


async def test_invalid_workflow_id_formats():
    """Test various invalid workflow ID formats."""
    
    # Create mock services
    mock_tenant_service = Mock()
    mock_user_service = Mock()
    mock_workflow_service = Mock()
    
    handler = WorkflowHandler(
        tenant_service=mock_tenant_service,
        user_service=mock_user_service,
        workflow_service=mock_workflow_service
    )
    
    # Mock context
    mock_context = Mock()
    mock_context.abort = Mock()
    
    test_cases = [
        ("", "Workflow ID is required"),
        ("invalid", "Invalid workflow ID format: invalid"),
        ("123", "Invalid workflow ID format: 123"),
        ("not-an-objectid", "Invalid workflow ID format: not-an-objectid"),
    ]
    
    print("Testing invalid workflow ID formats...")
    
    for invalid_id, expected_error in test_cases:
        print(f"  Testing ID: '{invalid_id}'")
        
        request = GetWorkflowRequest(id=invalid_id)
        
        try:
            await handler.GetWorkflow(request, mock_context)
        except Exception:
            pass  # Expected to fail
        
        # Check if abort was called with correct parameters
        if invalid_id == "":
            mock_context.abort.assert_called_with(
                grpc.StatusCode.INVALID_ARGUMENT,
                "Workflow ID is required"
            )
        else:
            mock_context.abort.assert_called_with(
                grpc.StatusCode.INVALID_ARGUMENT,
                f"Invalid workflow ID format: {invalid_id}"
            )
        
        mock_context.reset_mock()
        print(f"    ✅ Correctly handled: {expected_error}")


async def test_workflow_not_found():
    """Test workflow not found scenario."""
    
    # Create mock services
    mock_tenant_service = Mock()
    mock_tenant_service.get_tenant_info = AsyncMock()
    
    mock_user_service = Mock()
    
    mock_workflow_service = Mock()
    mock_workflow_service.get_workflow_by_id = AsyncMock(return_value=None)
    
    handler = WorkflowHandler(
        tenant_service=mock_tenant_service,
        user_service=mock_user_service,
        workflow_service=mock_workflow_service
    )
    
    # Mock context and auth
    mock_context = Mock()
    mock_context.abort = Mock()
    
    # Mock auth payload
    from unittest.mock import patch
    with patch('execution.v1.handlers.grpc.workflow_handler.get_and_validate_auth_payload') as mock_auth:
        mock_auth.return_value = Mock(org_id=ObjectId())
        
        valid_id = str(ObjectId())
        request = GetWorkflowRequest(id=valid_id)
        
        print(f"Testing workflow not found for valid ID: {valid_id}")
        
        try:
            await handler.GetWorkflow(request, mock_context)
        except Exception:
            pass  # Expected to fail
        
        # Check if abort was called with NOT_FOUND
        mock_context.abort.assert_called_with(
            grpc.StatusCode.NOT_FOUND,
            f"Workflow with ID {valid_id} not found"
        )
        
        print("    ✅ Correctly handled: Workflow not found")


async def main():
    """Run all tests."""
    print("🧪 Testing improved error handling in WorkflowHandler...")
    print()
    
    await test_invalid_workflow_id_formats()
    print()
    
    await test_workflow_not_found()
    print()
    
    print("✅ All error handling tests passed!")


if __name__ == "__main__":
    asyncio.run(main())
