# Vibe Automation Server

A Python monorepo for the vibe automation server, built with gRPC services and a modular architecture.

## 📋 Prerequisites

- **Python 3.13** (Required)
- **UV** (Python package manager)

## 🚀 Installation & Setup

### 1. Install UV (if not already installed)

```bash
# On macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### 2. Initialize Git Submodules

This project uses a git submodule for protobuf definitions:

```bash
git submodule init
git submodule update
```

### 4. Install Dependencies

```bash
# Install all dependencies including dev dependencies
uv sync --all-extras

# Or install only production dependencies
uv sync
```

If you run into issues here, try running `uv sync --reinstall` to clear the uv cache and then sync packages.

### 5. Generate Protobuf Files

Generate Python files from protobuf definitions:

```bash
uv run python -m scripts.generate_protos
```

### 6. Run the server

Currently we have two executable packages:

```bash
uv run execution # Runs the execution grpc server on port 9190
uv run web_server # Runs the web_server main.py entry point on port 8180
```

**Note:** To connect to the local web application launched with `yarn dev:local`, please follow these additional steps to configure the Envoy layer.

1. Install Envoy (if not already installed):

```bash
brew install envoy
```

2. Build a local certificate:

   - Create a directory named "cert". (If not already)

```bash
mkdir cert && cd cert
```

- Install mkcert:

```bash
brew install mkcert
```

- Generate certificates for localhost, 127.0.0.1

```bash
mkcert localhost 127.0.0.1
```

- Install the generated certificates:

```bash
mkcert -install
```

3. Run Envoy with the following configuration:

```bash
cd.. && envoy -c k8s/orby/envoy.local.yaml
```

## 🏗️ Project Structure

```
vibe-automation-server/
├── packages/
│   ├── common/          # Shared utilities and common code
│   ├── execution/       # Execution engine package
│   ├── web_server/      # Web server package
│   └── protos_gen/      # Protobuf package
│       └── protos_gen/
│           └── orby/    # Generated protobuf files (auto-generated)
│               └── va/  # VA service protobuf files
├── protos/              # Protobuf definitions (git submodule)
├── scripts/             # Utility scripts
└── pyproject.toml       # Project configuration and dependencies
```

## 🔧 Development Workflow

### Running Tests

```bash
# Run all tests
uv run pytest

# Run tests with verbose output
uv run pytest -v

# Run tests for a specific package
uv run pytest packages/execution/
```

### Code Formatting and Linting

```bash
# Format code
uvx ruff format

# Check linting
uvx ruff check

# Fix linting issues automatically
uvx ruff check --fix
```

or alternatively run,

```bash
bash scripts/lint.sh
```

## 📦 Package Management

### Adding Dependencies

#### Add to Root Project

```bash
# Add a production dependency
uv add package-name

# Add a development dependency
uv add --dev package-name

# Add with version constraint
uv add "package-name>=1.0.0"
```

#### Add to Specific Package

```bash
# Navigate to the package directory
cd packages/execution
uv add package-name

# Or add from root with specific pyproject.toml
uv add --project packages/execution package-name
```

### Removing Dependencies

```bash
# Remove from root project
uv remove package-name

# Remove from specific package
uv remove --project packages/execution package-name
```

### Adding New Packages to the Workspace

1. Create a new package directory with a subfolder of the same name:

```bash
uv init packages/new_package
cd packages/new_package
mkdir new_package  # Create subfolder with same name as package
```

This command will:
• Create the package directory at packages/new_package
• Generate a default pyproject.toml with the following configuration:
• Adds the subpackage to the workspace

2. **Configure the build system to expose the package properly**:

```toml
# In packages/new_package/pyproject.toml
[project]
name = "new_package"
version = "0.1.0"
requires-python = ">=3.13"
dependencies = []

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

# This is crucial - it tells the build system to include the subfolder
[tool.hatch.build.targets.wheel]
packages = ["new_package"]

# Add entry point if needed
[project.scripts]
new_package = "new_package.main:main"
```

3. **Create the package structure**:

```bash
# Create the main module structure (entry-point)
touch packages/new_package/new_package/main.py
```

4. **Add to ruff first-party imports**:

```toml
# In root pyproject.toml
[tool.ruff.lint.isort]
known-first-party = ["execution", "web_server", "common", "protos_gen", "new_package"]
```

## 🔗 Cross-Package Imports

### Import Workspace Packages

Since this is a monorepo with workspace configuration, you can import packages directly:

```python
# Examples from actual codebase:
from orby.va.public.execution_service_pb2_grpc import (
    add_ExecutionServiceServicer_to_server,
)
from common.interceptors.grpc.auth import AuthInterceptor
from orby.va.public.execution_messages_pb2 import StartExecutionRequest
```

### Importing Between Subpackages

When one subpackage needs to import from another subpackage, you need to:

1. **Add the dependency to the importing package's `pyproject.toml`**:

```toml
# In packages/execution/pyproject.toml (actual example)
[project]
name = "execution"
dependencies = [
    "common",      # Add this to import from common package
    "protos_gen",  # Add this to import from protos_gen package
]

[tool.uv.sources]
common = { workspace = true }
protos_gen = { workspace = true }
```

2. **Use the import in your code**:

```python
# In packages/execution/execution/v1/servers/grpc_server.py (actual example)
from common.interceptors.grpc.auth import AuthInterceptor
```

### Import Guidelines

#### 1. **Within the same package**: Use relative imports

```python
# In packages/execution/execution/v1/servers/grpc_server.py (actual example)
from ..config.settings import config
from ..handlers.grpc.execution_handler import ExecutionHandler
```

#### 2. **Across workspace packages**: Use absolute imports

```python
# In packages/execution/execution/v1/servers/grpc_server.py (actual examples)
from common.interceptors.grpc.auth import AuthInterceptor        # Workspace package
from orby.va.public.execution_service_pb2_grpc import add_ExecutionServiceServicer_to_server     # Generated protos
# Note: For protobuf package, you don't need to reference or import "protos_gen.orby...".
# This is because our build configuration only processes the nested "orby" folder inside the protos_gen package.
```
