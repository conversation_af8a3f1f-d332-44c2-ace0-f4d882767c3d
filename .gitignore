# Python-generated files
__pycache__/
*.py[cod]
*$py.class
build/
dist/
wheels/
*.egg-info/
.eggs/
*.egg
MANIFEST

# Virtual environments
.venv
venv/
env/
ENV/

# Testing and coverage
.pytest_cache/
.coverage
.coverage.*
*.cover
htmlcov/
.hypothesis/
.tox/
.nox/

# Certificates
cert/

# Linting and formatting
.ruff_cache/
.mypy_cache/
.dmypy.json
dmypy.json

# IDE and editors
.vscode/
.idea/
*.swp
*.swo
*~
.project
.pydevproject

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Documentation
docs/_build/
site/

# Package managers
.pdm.toml
.pdm-python
.pdm-build/
__pypackages__/

# Project specific
packages/protos_gen/protos_gen/orby
packages/protos_gen/protos_gen/common
.chainlit/

# Jupyter
.ipynb_checkpoints/
*.ipynb_checkpoints

# Databases
*.db
*.sqlite3
*.sqlite

# Temporary files
tmp/
temp/
*.tmp
*.bak
.cache/
.files/
local_storage/

.cursor

# Generated code
packages/agent/generated_code_*
runtime_workspace/
local_storage/
.files/

.claude/

# Local Environment
.env
